import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { LigneGarantieSchema } from "@/lib/schemas/ligne-garantie.schema";
import { Decimal } from "@prisma/client/runtime/library";

// GET: Récupérer une ligne de garantie par ID
export async function GET(request: Request, { params }: { params: { Id: string } }) {
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar", "AnalysteFinancier"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const resolvedParams = await params;
  const id = parseInt(resolvedParams.Id as string);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  try {
    const { searchParams } = new URL(request.url);
    const includeAvenants = searchParams.get('includeAvenants') === 'true';

    const ligneGarantie = await prisma.ligneGarantie.findUnique({
      where: { id },
      include: {
        bailleur: { select: { nom: true } },
        ...(includeAvenants && {
          avenants: {
            orderBy: { dateAvenant: 'desc' }
          }
        })
      }
    });

    if (!ligneGarantie) {
      return NextResponse.json({ message: "Ligne de garantie non trouvée" }, { status: 404 });
    }

    return NextResponse.json(ligneGarantie);
  } catch (error) {
    console.error("Erreur GET /api/lignes-garantie/[Id]:", error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

export async function PUT(request: Request, { params }: { params: { Id: string } }) {
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const resolvedParams = await params;
  const id = parseInt(resolvedParams.Id as string);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const body = await request.json();
  if (body.dateOuverture && typeof body.dateOuverture === "string") body.dateOuverture = new Date(body.dateOuverture);
  if (body.dateExpiration && typeof body.dateExpiration === "string") body.dateExpiration = new Date(body.dateExpiration);

  const validation = LigneGarantieSchema.safeParse(body);
  if (!validation.success) {
    console.error("Validation Zod échouée:", validation.error.formErrors.fieldErrors);
    return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
  }

  const {
    nom, bailleurId, referenceConvention, description, montantInitialStr,
    dateOuverture, dateExpiration, devise, statut,
    autreInfoObjectifLigne, autreInfoConditionsSpecifiques, autreInfoIndicateurPerf
  } = validation.data;

  const montantInitialDecimal = new Decimal(montantInitialStr.replace(',', '.'));
  const autresInformationsJson: any = {};
  if (autreInfoObjectifLigne) autresInformationsJson.objectif_ligne = autreInfoObjectifLigne;
  if (autreInfoConditionsSpecifiques) autresInformationsJson.conditions_specifiques = autreInfoConditionsSpecifiques;
  if (autreInfoIndicateurPerf) autresInformationsJson.indicateur_performance_cle = autreInfoIndicateurPerf;

  try {
    const updated = await prisma.ligneGarantie.update({
      where: { id },
      data: {
        nom,
        bailleurId: parseInt(bailleurId),
        referenceConvention,
        description,
        montantInitial: montantInitialDecimal,
        dateOuverture,
        dateExpiration,
        devise: devise.toUpperCase(),
        statut: statut as any, // Replace 'any' with 'StatutLigneGarantie' if you have the enum imported
        autresInformations: Object.keys(autresInformationsJson).length > 0 ? autresInformationsJson : null,
      },
    });
    return NextResponse.json(updated);
  } catch (error: any) {
    return NextResponse.json({ message: error.message || "Erreur interne" }, { status: 500 });
  }
}