// src/app/api/allocations/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { RoleUtilisateur, StatutAllocation, Periodicite, StatutLigneGarantie } from "@prisma/client";
import { AllocationLignePartenaireSchema } from "@/lib/schemas/allocation-ligne-partenaire.schema";
import { auditContext } from '@/lib/prisma-audit.middleware';
import { headers } from 'next/headers';
import { Decimal } from "@prisma/client/runtime/library";

interface RouteParams { params: { id: string } }

// GET: Récupérer une allocation spécifique avec ses détails et avenants
export async function GET(request: Request, { params }: RouteParams) {
  const session = await getServerSession(authOptions);
  // Ajuster les rôles si nécessaire pour qui peut voir les détails d'une allocation
  if (!session || !["Administrateur", "GestionnaireGesGar", "AnalysteFinancier", "Partenaire"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const resolvedParams = await params;
  const allocationId = parseInt(resolvedParams.id);
  if (isNaN(allocationId)) {
    return NextResponse.json({ message: "ID d'allocation invalide" }, { status: 400 });
  }

  // Logique de restriction pour le rôle Partenaire (ne voir que ses propres allocations)
  const whereClauseForPartenaire: any = {};
  if (session.user?.role === "Partenaire") {
    // Supposons que vous avez un moyen de lier l'Utilisateur Partenaire à son enregistrement Partenaire
    // Exemple : const userWithPartenaire = await prisma.utilisateur.findUnique({ where: { id: parseInt(session.user.id) }, select: { partenaireId: true }});
    // if (userWithPartenaire?.partenaireId) {
    //   whereClauseForPartenaire.partenaireId = userWithPartenaire.partenaireId;
    // } else {
    //   return NextResponse.json({ message: "Partenaire associé non trouvé pour cet utilisateur." }, { status: 403 });
    // }
    console.warn("Filtrage par partenaire non implémenté pour GET /api/allocations/[id] pour le rôle Partenaire");
  }

  try {
    const allocation = await prisma.allocationLignePartenaire.findUnique({
      where: {
        id: allocationId,
        ...whereClauseForPartenaire // Appliquer le filtre si c'est un partenaire
      },
      include: {
        ligneGarantie: { // Infos de la ligne parente
          select: { id: true, nom: true, devise: true, montantInitial: true, dateExpiration: true }
        },
        partenaire: { // Infos du partenaire
          select: { id: true, nom: true, typePartenaire: true }
        },
        avenants: { // Liste des avenants de cette allocation
          orderBy: { dateAvenant: 'desc' },
          include: { utilisateurCreation: { select: { nomUtilisateur: true } } }
        },
        // garanties: { // Optionnel: pour un résumé des garanties liées
        //   select: { id: true, referenceGarantie: true, montantGarantie: true, statut: true },
        //   take: 5, // Limiter pour ne pas surcharger
        //   orderBy: { dateCreation: 'desc' }
        // },
        // paiementsInteretsCommissions: { // Optionnel: pour un résumé des paiements
        //   orderBy: { datePaiement: 'desc' },
        //   take: 5
        // },
        utilisateurCreation: { select: { nomUtilisateur: true, nom: true, prenom: true } },
        utilisateurModification: { select: { nomUtilisateur: true, nom: true, prenom: true } },
      }
    });

    if (!allocation) {
      return NextResponse.json({ message: "Allocation non trouvée" }, { status: 404 });
    }
    return NextResponse.json(allocation);
  } catch (error) {
    console.error(`Erreur GET /api/allocations/${allocationId}:`, error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// PUT: Mettre à jour une allocation
export async function PUT(request: Request, { params }: RouteParams) {
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const resolvedParams = await params;
  const id = parseInt(resolvedParams.id); // ID de l'allocation à modifier
  if (isNaN(id)) return NextResponse.json({ message: "ID d'allocation invalide" }, { status: 400 });

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;
  // ... (headers pour audit) ...

  return auditContext.run({ /* ... */ }, async () => {
    try {
      const body = await request.json();
      if (body.dateAllocation) body.dateAllocation = new Date(body.dateAllocation);
      if (body.dateExpiration) body.dateExpiration = new Date(body.dateExpiration);

      const validation = AllocationLignePartenaireSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const {
        ligneGarantieId, partenaireId, referenceConvention, montantAlloueStr,
        dateAllocation, dateExpiration, statut, tauxCouvertureMaxStr,
        tauxInteretStr, tauxCommissionStr, periodicitePaiementInteret,
        periodicitePaiementCommission, commentaires
      } = validation.data;

      const montantAlloueDecimalNouveau = new Decimal(montantAlloueStr.replace(',', '.'));
      const ligneGarantieIdNumNouveau = parseInt(ligneGarantieId);
      const partenaireIdNumNouveau = parseInt(partenaireId);

      // --- Début de la logique transactionnelle pour PUT ---
      const result = await prisma.$transaction(async (tx) => {
        // 1. Récupérer l'allocation existante
        const allocationExistante = await tx.allocationLignePartenaire.findUnique({ where: { id } });
        if (!allocationExistante) throw new Error("Allocation à modifier non trouvée.");

        const ligneGarantieActuelleId = allocationExistante.ligneGarantieId;
        const montantAlloueAncien = allocationExistante.montantAlloue;

        // 2. Récupérer la ligne de garantie parente ACTUELLE (pour restituer l'ancien montant)
        const ligneGarantieActuelle = await tx.ligneGarantie.findUnique({ where: { id: ligneGarantieActuelleId } });
        if (!ligneGarantieActuelle) throw new Error("Ancienne ligne de garantie parente non trouvée.");

        // 3. Récupérer la NOUVELLE ligne de garantie parente (peut être la même ou différente)
        const ligneGarantieNouvelle = await tx.ligneGarantie.findUnique({ where: { id: ligneGarantieIdNumNouveau } });
        if (!ligneGarantieNouvelle) throw new Error("Nouvelle ligne de garantie parente non trouvée.");
         if (ligneGarantieNouvelle.statut !== StatutLigneGarantie.Active && ligneGarantieNouvelle.statut !== StatutLigneGarantie.EnAttenteValidation && ligneGarantieNouvelle.statut !== StatutLigneGarantie.Epuisee) {
            throw new Error(`La nouvelle ligne de garantie '${ligneGarantieNouvelle.nom}' n'est pas dans un statut permettant des allocations.`);
        }


        // 4. Calculer le différentiel de montant à ajuster sur les lignes
        const montantDisponibleLigneActuelle = ligneGarantieActuelle.montantDisponible.add(montantAlloueAncien); // Restituer l'ancien montant

        // 5. Vérifier la disponibilité sur la NOUVELLE ligne de garantie
        // Si la ligne est la même, montantDisponibleLigneActuelle contient déjà le montant restitué
        const montantDisponiblePourVerification = (ligneGarantieActuelleId === ligneGarantieIdNumNouveau)
                                                ? montantDisponibleLigneActuelle
                                                : ligneGarantieNouvelle.montantDisponible;

        if (montantAlloueDecimalNouveau.greaterThan(montantDisponiblePourVerification)) {
          throw new Error(`Nouveau montant alloué (${montantAlloueDecimalNouveau}) dépasse le montant disponible (${montantDisponiblePourVerification}) sur la nouvelle ligne de garantie.`);
        }
        const montantDisponibleLigneNouvelle = montantDisponiblePourVerification.sub(montantAlloueDecimalNouveau); // Déduire le nouveau montant


        // 6. Mettre à jour l'ancienne ligne (si différente de la nouvelle)
        if (ligneGarantieActuelleId !== ligneGarantieIdNumNouveau) {
          await tx.ligneGarantie.update({
            where: { id: ligneGarantieActuelleId },
            data: { montantDisponible: montantDisponibleLigneActuelle, utilisateurModificationId: modifierId },
          });
        }

        // 7. Mettre à jour la nouvelle ligne (ou la même ligne avec le nouveau solde)
        await tx.ligneGarantie.update({
          where: { id: ligneGarantieIdNumNouveau },
          data: { montantDisponible: montantDisponibleLigneNouvelle, utilisateurModificationId: modifierId },
        });

        // 8. Mettre à jour l'allocation
        // Le montantDisponible de l'allocation doit aussi être recalculé si le montantAlloue change
        // et qu'il y a déjà des garanties. Pour l'instant, on le réinitialise si le montant alloué change.
        // Une logique plus fine serait: nouveauMontantDisponibleAlloc = nouveauMontantAlloue - sommeGarantiesActivesSurAlloc
        let nouveauMontantDisponibleAllocation = allocationExistante.montantDisponible;
        if (!montantAlloueAncien.equals(montantAlloueDecimalNouveau)) {
            // Si le montant alloué change, on recalcule le disponible de l'allocation
            // Pour l'instant, simplification : nouveau disponible = nouveau alloué - (ancien alloué - ancien disponible)
            // Ceci suppose que la différence (ancien alloué - ancien disponible) est le montant déjà "consommé" par les garanties
            const consommeSurAllocation = montantAlloueAncien.sub(allocationExistante.montantDisponible);
            nouveauMontantDisponibleAllocation = montantAlloueDecimalNouveau.sub(consommeSurAllocation);
            if (nouveauMontantDisponibleAllocation.isNegative()) {
                // Gérer ce cas : on ne peut pas allouer moins que ce qui est déjà consommé
                throw new Error("Le nouveau montant alloué est inférieur au montant déjà consommé par les garanties sur cette allocation.");
            }
        }


        const updatedAllocation = await tx.allocationLignePartenaire.update({
          where: { id },
          data: {
            ligneGarantieId: ligneGarantieIdNumNouveau,
            partenaireId: partenaireIdNumNouveau, // Assurez-vous que partenaireIdNumNouveau est défini
            referenceConvention,
            montantAlloue: montantAlloueDecimalNouveau,
            montantDisponible: nouveauMontantDisponibleAllocation,
            dateAllocation,
            dateExpiration: dateExpiration || null,
            statut: statut as StatutAllocation,
            tauxCouvertureMax: tauxCouvertureMaxStr ? new Decimal(tauxCouvertureMaxStr.replace(',', '.')) : null,
            tauxInteret: tauxInteretStr ? new Decimal(tauxInteretStr.replace(',', '.')) : null,
            tauxCommission: tauxCommissionStr ? new Decimal(tauxCommissionStr.replace(',', '.')) : null,
            periodicitePaiementInteret: periodicitePaiementInteret ? periodicitePaiementInteret as Periodicite : null,
            periodicitePaiementCommission: periodicitePaiementCommission ? periodicitePaiementCommission as Periodicite : null,
            commentaires,
            utilisateurModificationId: modifierId,
          },
        });
        return updatedAllocation;
      });
      // --- Fin de la logique transactionnelle pour PUT ---
      return NextResponse.json(result);

    } catch (error: any) {
      console.error(`Erreur PUT /api/allocations/${id}:`, error);
      if (error.message.includes("dépasse le montant disponible") || error.message.includes("non trouvée") || error.message.includes("non valide")) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      if (error.code === 'P2025') return NextResponse.json({ message: "Allocation non trouvée" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}

// DELETE: Supprimer une allocation
export async function DELETE(request: Request, { params }: RouteParams) {
  const session = await getServerSession(authOptions);
  if (!session || !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }
  const resolvedParams = await params;
  const id = parseInt(resolvedParams.id);
  if (isNaN(id)) return NextResponse.json({ message: "ID invalide" }, { status: 400 });

  const deleterId = session.user?.id ? parseInt(session.user.id) : undefined;
  // ... (headers pour audit) ...

  return auditContext.run({ /* ... */ }, async () => {
    try {
      const result = await prisma.$transaction(async (tx) => {
        // 1. Récupérer l'allocation à supprimer pour connaître son montant et sa ligne parente
        const allocationASupprimer = await tx.allocationLignePartenaire.findUnique({ where: { id } });
        if (!allocationASupprimer) throw new Error("Allocation non trouvée.");

        // 2. Vérifier si des garanties sont liées à cette allocation
        const garantiesCount = await tx.garantie.count({ where: { allocationId: id, statut: { notIn: [/* Statuts clos/annulés */ 'Echue', 'MainleveeAccordee', 'Radiee' ]}}});
        if (garantiesCount > 0) {
          throw new Error(`Impossible de supprimer: ${garantiesCount} garantie(s) active(s) ou en instruction sont encore liées à cette allocation.`);
        }

        // 3. Récupérer la ligne de garantie parente
        const ligneGarantie = await tx.ligneGarantie.findUnique({ where: { id: allocationASupprimer.ligneGarantieId } });
        if (!ligneGarantie) throw new Error("Ligne de garantie parente non trouvée.");

        // 4. Restituer le montant alloué au disponible de la ligne parente
        const nouveauMontantDisponibleLigne = ligneGarantie.montantDisponible.add(allocationASupprimer.montantAlloue);
        await tx.ligneGarantie.update({
          where: { id: ligneGarantie.id },
          data: { montantDisponible: nouveauMontantDisponibleLigne, utilisateurModificationId: deleterId },
        });

        // 5. Supprimer l'allocation
        await tx.allocationLignePartenaire.delete({ where: { id } });
        return { message: "Allocation supprimée avec succès" };
      });
      return NextResponse.json(result, { status: 200 });
    } catch (error: any) {
      console.error(`Erreur DELETE /api/allocations/${id}:`, error);
      if (error.message.includes("garantie(s) active(s)") || error.message.includes("non trouvée")) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      if (error.code === 'P2025') return NextResponse.json({ message: "Allocation non trouvée" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  });
}