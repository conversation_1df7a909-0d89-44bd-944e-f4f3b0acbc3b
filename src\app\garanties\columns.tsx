// src/app/(app)/garanties/columns.tsx
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Garantie, AllocationLignePartenaire, LigneGarantie, Partenaire, Projet, ClientBeneficiaire, StatutGarantie, Utilisateur, SecteurActivite } from "@prisma/client";
import { ArrowUpDown, Edit, Trash2, Eye, Handshake, AlertOctagon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";

// Type enrichi pour la colonne, incluant les relations nécessaires pour l'affichage
export type GarantieColumn = Garantie & {
    allocation: AllocationLignePartenaire & {
        partenaire: Pick<Partenaire, "id" | "nom">;
        ligneGarantie: Pick<LigneGarantie, "id" | "nom" | "devise">;
    };
    projet: Projet & {
        clientBeneficiaire: Pick<ClientBeneficiaire, "id" | "nomOuRaisonSociale">;
        secteurActivite: Pick<SecteurActivite, "id" | "nom">;
    };
    utilisateurCreation?: Pick<Utilisateur, "nomUtilisateur"> | null;
};

interface GarantieColumnsProps {
    onEdit: (garantie: GarantieColumn) => void;
    onDelete: (garantie: GarantieColumn) => void; // Ou onDeactivate si suppression logique
    onDemandeMainlevee: (garantie: GarantieColumn) => void;
    onDemandeMiseEnJeu: (garantie: GarantieColumn) => void;
    // onViewDetails?: (garantie: GarantieColumn) => void;
}

const formatCurrency = (amount: any, currency: string = "XOF") => {
    if (amount === null || amount === undefined) return "-";
   const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    if (isNaN(num)) return "-";
   return new Intl.NumberFormat("fr-FR", { 
     style: "currency", 
     currency: currency, 
     minimumFractionDigits: 0,
     // Ensure consistent display format while keeping fr-FR for currency symbol
   }).format(num);
};

const formatDate = (date: any) => date ? new Date(date).toLocaleDateString('fr-FR') : '-';

const getStatutGarantieBadgeVariant = (statut: StatutGarantie | undefined) => {
    switch (statut) {
        case StatutGarantie.Active: return "bg-green-100 text-green-700 border-green-300";
         case StatutGarantie.EnInstruction: return "bg-yellow-100 text-yellow-700 border-yellow-300";
        case StatutGarantie.MainleveeAccordee: return "bg-blue-100 text-blue-700 border-blue-300";
        case StatutGarantie.EnSouffrance: case StatutGarantie.MiseEnJeuDemandee: return "bg-orange-100 text-orange-700 border-orange-300";
        case StatutGarantie.MiseEnJeuPayee: case StatutGarantie.ClotureeAnormalement: case StatutGarantie.Radiee: return "bg-red-100 text-red-700 border-red-300";
        default: return "bg-slate-100 text-slate-700 border-slate-300";
    }
};

export const getGarantieColumns = ({ onEdit, onDelete, onDemandeMainlevee, onDemandeMiseEnJeu }: GarantieColumnsProps): ColumnDef<GarantieColumn>[] => [
  {
    accessorKey: "referenceGarantie",
    header: ({ column }) => (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
            Référence Garantie
            <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
    ),
    cell: ({ row }) => (
        <Link href={`/garanties/${row.original.id}` as any} className="hover:underline text-blue-600 font-mono text-xs" title="Voir les détails de la garantie">
            {row.getValue("referenceGarantie")}
        </Link>
    )
  },
  {
    accessorKey: "projet.clientBeneficiaire.nomOuRaisonSociale",
    header: "Client Bénéficiaire",
    cell: ({ row }) => row.original.projet.clientBeneficiaire.nomOuRaisonSociale,
  },
  {
    accessorKey: "projet.nom",
    header: "Projet",
    cell: ({ row }) => row.original.projet.nom,
  },
  {
    accessorKey: "allocation.partenaire.nom",
    header: "Partenaire",
    cell: ({ row }) => row.original.allocation.partenaire.nom,
  },
  {
    accessorKey: "montantCredit",
    header: "Montant Crédit",
    cell: ({ row }) => formatCurrency(row.getValue("montantCredit"), row.original.allocation.ligneGarantie.devise),
  },
  {
    accessorKey: "montantGarantie",
    header: "Montant Garanti",
    cell: ({ row }) => formatCurrency(row.getValue("montantGarantie"), row.original.allocation.ligneGarantie.devise),
  },
  {
    accessorKey: "statut",
    header: "Statut",
    cell: ({ row }) => <Badge className={getStatutGarantieBadgeVariant(row.getValue("statut"))}>{row.getValue("statut")}</Badge>,
  },
  {
    accessorKey: "dateEcheanceGarantie",
    header: "Échéance Garantie",
    cell: ({ row }) => formatDate(row.getValue("dateEcheanceGarantie")),
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const garantie = row.original;
      
      // Debug amélioré pour diagnostiquer le problème
      console.log('=== DEBUG MAINLEVÉE ===');
      console.log('Garantie ID:', garantie.id);
      console.log('Référence:', garantie.referenceGarantie);
      console.log('Statut brut:', garantie.statut);
      console.log('Type de statut:', typeof garantie.statut);
      
      const statutsPermettantDemandeMainlevee = [
        StatutGarantie.Active,
        StatutGarantie.EnSouffrance,
        StatutGarantie.Echue,
        StatutGarantie.MiseEnJeuAcceptee,
        StatutGarantie.MiseEnJeuPayee,
        StatutGarantie.Transferree,
      ];
      
      console.log('Statuts autorisés:', statutsPermettantDemandeMainlevee);
      console.log('Statut en string:', String(garantie.statut));
      console.log('Inclus dans autorisés:', statutsPermettantDemandeMainlevee.map(String).includes(String(garantie.statut)));
      console.log('Pas MainleveeDemandee:', garantie.statut !== StatutGarantie.MainleveeDemandee);
      console.log('Pas MainleveeAccordee:', garantie.statut !== StatutGarantie.MainleveeAccordee);
      
      const peutDemanderMainlevee = statutsPermettantDemandeMainlevee.map(String).includes(String(garantie.statut))
        && garantie.statut !== StatutGarantie.MainleveeDemandee
        && garantie.statut !== StatutGarantie.MainleveeAccordee;
        
      console.log('Résultat final peutDemanderMainlevee:', peutDemanderMainlevee);
      console.log('========================');

      // Conditions pour afficher le bouton "Demander Mise en Jeu"
      const peutDemanderMiseEnJeu = ([
        StatutGarantie.Active,
        StatutGarantie.EnSouffrance,
      ] as StatutGarantie[]).includes(garantie.statut as StatutGarantie) && garantie.statut !== StatutGarantie.MiseEnJeuDemandee;

      return (
        <div className="flex space-x-1 justify-end">
          {/* <Link href={`/garanties/${garantie.id}`}>
            <Button variant="ghost" size="icon" title="Voir Détails"><Eye className="h-4 w-4" /></Button>
          </Link> */}
          <Button variant="ghost" size="icon" onClick={() => onEdit(garantie)} title="Modifier (Statut/Suivi)">
            <Edit className="h-4 w-4" />
          </Button>
          {peutDemanderMainlevee && (
            <Button variant="ghost" size="icon" onClick={() => onDemandeMainlevee(garantie)} title="Demander Mainlevée" className="text-green-600 hover:text-green-700">
              <Handshake className="h-4 w-4" />
            </Button>
          )}
          {peutDemanderMiseEnJeu && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDemandeMiseEnJeu(garantie)}
              title={garantie.statut === "MiseEnJeuDemandee" ? "Déjà en cours" : "Demander Mise en Jeu"}
              className="text-orange-600 hover:text-orange-700"
              disabled={garantie.statut === "MiseEnJeuDemandee"}
            >
              <AlertOctagon className="h-4 w-4" />
            </Button>
          )}
          {garantie.statut === "MiseEnJeuDemandee" && (
            <Badge variant="outline" className="ml-2">Mise en jeu en cours</Badge>
          )}
          {/* La suppression d'une garantie est complexe (mainlevée, etc.), à gérer spécifiquement */}
          {/* <Button variant="ghost" size="icon" onClick={() => onDelete(garantie)} className="text-red-600 hover:text-red-700" title="Annuler/Radier">
            <Trash2 className="h-4 w-4" />
          </Button> */}
        </div>
      );
    },
  },
];

export { formatCurrency };