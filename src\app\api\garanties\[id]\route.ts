// src/app/api/garanties/[id]/route.ts
import { NextResponse } from "next/server";
import prisma, { Prisma } from "@/lib/prisma";
 import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Ajustez le chemin si nécessaire
import { RoleUtilisateur, StatutGarantie } from "@prisma/client";
import { headers } from "next/headers";
import { withAudit, BusinessAction } from '@/lib/audit-wrapper';
import { UpdateGarantieSchema } from "@/lib/schemas/garantie.schema";
import { Decimal } from "@prisma/client/runtime/library";

type RouteParams = { params: { id: string } };

export async function GET(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  // Ajuster les rôles autorisés selon qui peut voir les détails d'une garantie
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar", "AnalysteFinancier", "Partenaire"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé" }, { status: 403 });
  }

  const garantieId = parseInt(resolvedParams.id);
  if (isNaN(garantieId)) {
    return NextResponse.json({ message: "ID de garantie invalide" }, { status: 400 });
  }

  // Pour le rôle Partenaire, s'assurer qu'il ne voit que ses propres garanties
  const whereClauseForPartenaire: any = {};
  if (session.user?.role === RoleUtilisateur.Partenaire) {
    // On cherche le partenaire associé à cet utilisateur
    const partenaire = await prisma.partenaire.findFirst({
      where: { utilisateurCreationId: parseInt(session.user.id) }
    });
    if (!partenaire) {
      console.error(`[SECURITE] Utilisateur Partenaire sans lien Partenaire: id=${session.user.id}`);
      return NextResponse.json({ message: "Aucun partenaire associé à cet utilisateur" }, { status: 403 });
    }
    whereClauseForPartenaire.partenaireId = partenaire.id;
  }


  try {
    const garantie = await prisma.garantie.findUnique({
      where: {
        id: garantieId,
        ...whereClauseForPartenaire // Appliquer le filtre si c'est un partenaire
      },
      include: {
        ligneGarantie: { select: { id: true, nom: true } },
        allocation: {
          include: {
            partenaire: { select: { id: true, nom: true } },
            ligneGarantie: { select: { id: true, nom: true, devise: true } }, // Pour la devise
          }
        },
        // partenaire: { select: { id: true, nom: true } }, // Redondant si inclus via allocation
        projet: {
          include: {
            clientBeneficiaire: { select: { id: true, nomOuRaisonSociale: true } },
            secteurActivite: { select: { id: true, nom: true } },
          }
        },
        // clientBeneficiaire: { select: { id: true, nomOuRaisonSociale: true } }, // Redondant si inclus via projet
        utilisateurCreation: { select: { nomUtilisateur: true, nom: true, prenom: true } },
        utilisateurModification: { select: { nomUtilisateur: true, nom: true, prenom: true } },
      },
    });

    if (!garantie) {
      return NextResponse.json({ message: "Garantie non trouvée" }, { status: 404 });
    }
    return NextResponse.json(garantie);
  } catch (error) {
    console.error(`Erreur lors de la récupération de la garantie ${garantieId}:`, error);
    return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
  }
}

// src/app/api/garanties/[id]/route.ts
// ... (imports existants, y compris Decimal, GarantieSchema, UpdateGarantieSchema, auditContext, headers) ...

// ... (fonction GET existante) ...

export async function PUT(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (!session || ![RoleUtilisateur.Administrateur, RoleUtilisateur.GestionnaireGesGar].includes(session.user?.role as RoleUtilisateur)) {
    return NextResponse.json({ message: "Non autorisé à modifier cette garantie" }, { status: 403 });
  }

  const garantieId = parseInt(resolvedParams.id);
  if (isNaN(garantieId)) {
    return NextResponse.json({ message: "ID de garantie invalide" }, { status: 400 });
  }

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;

  return withAudit(async () => {
    try {
      const body = await request.json();

      // Convertir les dates string en objets Date avant la validation Zod
      if (body.dateEcheanceGarantie) body.dateEcheanceGarantie = new Date(body.dateEcheanceGarantie);
      if (body.dateAccordGarantie) body.dateAccordGarantie = new Date(body.dateAccordGarantie);
      if (body.dateEffetGarantie) body.dateEffetGarantie = new Date(body.dateEffetGarantie);
      if (body.dateDernierRemboursementClient) body.dateDernierRemboursementClient = new Date(body.dateDernierRemboursementClient);


      const validation = UpdateGarantieSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json({ message: "Données de mise à jour invalides", errors: validation.error.formErrors.fieldErrors }, { status: 400 });
      }

      const validatedData = validation.data;
      const dataToUpdate: Prisma.GarantieUpdateInput = {}; // Type Prisma pour les données de mise à jour

      // --- Préparation des données à mettre à jour ---
      if (validatedData.statut) dataToUpdate.statut = validatedData.statut as StatutGarantie;
      if (validatedData.identifiantCreditPartenaire !== undefined) dataToUpdate.identifiantCreditPartenaire = validatedData.identifiantCreditPartenaire;
      if (validatedData.conditionsParticulieres !== undefined) dataToUpdate.conditionsParticulieres = validatedData.conditionsParticulieres;
      if (validatedData.delaiMiseEnJeu !== undefined) dataToUpdate.delaiMiseEnJeu = parseInt(validatedData.delaiMiseEnJeu);
      if (validatedData.dateEcheanceGarantie) dataToUpdate.dateEcheanceGarantie = validatedData.dateEcheanceGarantie;
      if (validatedData.dateAccordGarantie) dataToUpdate.dateAccordGarantie = validatedData.dateAccordGarantie;
      if (validatedData.dateEffetGarantie) dataToUpdate.dateEffetGarantie = validatedData.dateEffetGarantie;

      // Champs de suivi
      if (validatedData.dateDernierRemboursementClient) dataToUpdate.dateDernierRemboursementClient = validatedData.dateDernierRemboursementClient;
      if (validatedData.montantRestantDuCreditStr !== undefined && validatedData.montantRestantDuCreditStr.trim() !== "") {
        dataToUpdate.montantRestantDuCredit = new Decimal(validatedData.montantRestantDuCreditStr.replace(',', '.'));
      } else if (validatedData.montantRestantDuCreditStr === "") { // Permettre de vider le champ
         dataToUpdate.montantRestantDuCredit = null;
      }
      if (validatedData.nombreEcheancesImpayeesStr !== undefined && validatedData.nombreEcheancesImpayeesStr.trim() !== "") {
        dataToUpdate.nombreEcheancesImpayees = parseInt(validatedData.nombreEcheancesImpayeesStr);
      } else if (validatedData.nombreEcheancesImpayeesStr === "") { // Permettre de vider le champ
         dataToUpdate.nombreEcheancesImpayees = null; // Ou 0 selon la logique métier
      }


      // --- Logique de Transition de Statut et Impact sur Allocation ---
      const garantieActuelle = await prisma.garantie.findUnique({
        where: { id: garantieId },
        include: { allocation: true } // Inclure l'allocation pour mettre à jour son disponible
      });

      if (!garantieActuelle) {
        throw new Error("Garantie non trouvée pour la mise à jour.");
      }
      if (!garantieActuelle.allocation) { // Vérification de sécurité
        throw new Error("Allocation parente non trouvée pour cette garantie.");
      }

      const ancienStatut = garantieActuelle.statut;
      const nouveauStatut = validatedData.statut as StatutGarantie; // Le nouveau statut vient du formulaire

      // Définir quels statuts "consomment" le montant de l'allocation
      const statutsConsommateurs = [
        StatutGarantie.Echue, // Si Echue réserve déjà le montant
        StatutGarantie.Active,
        StatutGarantie.EnSouffrance,
        StatutGarantie.MiseEnJeuDemandee,
        StatutGarantie.MiseEnJeuAcceptee,
        // MiseEnJeuPayee ne restitue pas, le montant est sorti
      ];
      // Définir quels statuts "libèrent" le montant (si précédemment consommé)
      const statutsLiberateurs = [
        StatutGarantie.Echue,
        StatutGarantie.MainleveeAccordee,
        StatutGarantie.Radiee, // Annulation avant consommation réelle
        StatutGarantie.ClotureeAnormalement, // Peut-être, selon les règles
        StatutGarantie.MiseEnJeuRefusee, // Si elle était précédemment dans un statut consommateur
      ];

      const ancienStatutConsommait = statutsConsommateurs.includes(ancienStatut);
      const nouveauStatutConsomme = statutsConsommateurs.includes(nouveauStatut);
      const nouveauStatutLibere = statutsLiberateurs.includes(nouveauStatut);

      let montantImpactAllocation = new Decimal(0);

      if (ancienStatutConsommait && nouveauStatutLibere) {
        // La garantie était active/consommatrice et devient libérée -> restituer le montant
        montantImpactAllocation = garantieActuelle.montantGarantie; // Montant à ajouter au disponible de l'allocation
      } else if (!ancienStatutConsommait && nouveauStatutConsomme) {
        // La garantie n'était pas consommatrice et le devient -> déduire le montant
        // Vérifier d'abord la disponibilité sur l'allocation
        if (garantieActuelle.montantGarantie.greaterThan(garantieActuelle.allocation.montantDisponible)) {
          throw new Error(`Activation impossible: Montant de garantie (${garantieActuelle.montantGarantie.toFixed(2)}) dépasse le disponible (${garantieActuelle.allocation.montantDisponible.toFixed(2)}) de l'allocation.`);
        }
        montantImpactAllocation = garantieActuelle.montantGarantie.negated(); // Montant à soustraire du disponible de l'allocation
      }
      // Autres cas :
      // - ancien consommait ET nouveau consomme : pas de changement de l'impact sur le disponible de l'allocation par le statut.
      // - ancien ne consommait pas ET nouveau ne consomme/libère pas : pas de changement.

      // --- Début Transaction ---
      const updatedGarantie = await prisma.$transaction(async (tx) => {
        if (!montantImpactAllocation.isZero()) { // Si le disponible de l'allocation doit changer
          await tx.allocationLignePartenaire.update({
            where: { id: garantieActuelle.allocationId },
            data: {
              montantDisponible: garantieActuelle.allocation.montantDisponible.add(montantImpactAllocation),
              utilisateurModificationId: modifierId,
            },
          });
        }

        // Mettre à jour la garantie
        return tx.garantie.update({
          where: { id: garantieId },
          data: {
            ...dataToUpdate,
            utilisateurModificationId: modifierId,
          },
        });
      });
      // --- Fin Transaction ---

      return NextResponse.json(updatedGarantie);

    } catch (error: any) {
      console.error(`Erreur PUT /api/garanties/${garantieId}:`, error);
      if (error.message.includes("dépasse le disponible") || error.message.includes("non trouvée") || error.message.includes("Activation impossible")) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
      if (error.code === 'P2025') return NextResponse.json({ message: "Garantie ou entité liée non trouvée" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  }, {
    module: 'GARANTIES',
    operation: BusinessAction.GARANTIE_UPDATE,
    resourceId: garantieId.toString(),
    metadata: { modifierId }
  });
}

export async function PATCH(request: Request, { params }: RouteParams) {
  const resolvedParams = await params;
  const session = await getServerSession(authOptions);
  if (
    !session ||
    !["Administrateur", "GestionnaireGesGar"].includes(session.user?.role as string)
  ) {
    return NextResponse.json({ message: "Non autorisé à supprimer cette garantie" }, { status: 403 });
  }

  const garantieId = parseInt(resolvedParams.id);
  if (isNaN(garantieId)) {
    return NextResponse.json({ message: "ID de garantie invalide" }, { status: 400 });
  }

  const modifierId = session.user?.id ? parseInt(session.user.id) : undefined;

  return withAudit(async () => {
    try {
      // Ici, on force le statut à "Supprimee"
      const updatedGarantie = await prisma.garantie.update({
        where: { id: garantieId },
        data: {
          statut: "Supprimee",
          utilisateurModificationId: modifierId,
        },
      });
      return NextResponse.json(updatedGarantie);
    } catch (error: any) {
      console.error(`Erreur PATCH (soft delete) /api/garanties/${garantieId}:`, error);
      if (error.code === 'P2025') return NextResponse.json({ message: "Garantie non trouvée" }, { status: 404 });
      return NextResponse.json({ message: "Erreur interne du serveur" }, { status: 500 });
    }
  }, {
    module: 'GARANTIES',
    operation: BusinessAction.GARANTIE_DELETE,
    resourceId: garantieId.toString(),
    metadata: { modifierId }
  });
}

// TODO: Fonction DELETE (ou plutôt des actions spécifiques comme Radier, Demander Mainlevée etc.)