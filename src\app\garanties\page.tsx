// src/app/(app)/garanties/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { RoleUtilisateur, TypeGarantie } from "@/types/enums";
import { StatutGarantie, TypeMainlevee } from "@/types/enums";
import { getGarantieColumns, GarantieColumn, formatCurrency } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { GarantieFormValues, GarantieSchema, UpdateGarantieFormValues, UpdateGarantieSchema } from "@/lib/schemas/garantie.schema";
import { useF<PERSON>, SubmitHandler, Control } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from "@/components/ui/alert-dialog";
import { PlusCircle, Eye, Edit } from "lucide-react";
import Decimal from "decimal.js";
import { DemandeMainleveeFormValues, DemandeMainleveeSchema } from "@/lib/schemas/mainlevee.schema";
import { z } from "zod";
import { DemandeMiseEnJeuFormValues, DemandeMiseEnJeuSchema } from "@/lib/schemas/mise-en-jeu.schema";
import useSWR from "swr";

// Les rôles autorisés à accéder à la page
const ROLES_AUTORISES: RoleUtilisateur[] = [
  RoleUtilisateur.Administrateur,
  RoleUtilisateur.GestionnaireGesGar,
  RoleUtilisateur.AnalysteFinancier,
  RoleUtilisateur.Partenaire,
  RoleUtilisateur.Bailleur,
  RoleUtilisateur.Auditeur,
];

type SelectOption = { value: string; label: string; disabled?: boolean; detail?: string };
const typeGarantieOptions = Object.values(TypeGarantie).map(val => ({ value: val, label: val }));
// Pour le statut, on pourrait vouloir limiter les options lors de la création
const statutGarantieCreationOptions = [StatutGarantie.EnInstruction, StatutGarantie.Echue, StatutGarantie.Active].map(val => ({ value: val, label: val }));

const statutGarantieOptionsAll = Object.values(StatutGarantie).map(val => ({ value: val, label: val }));

const validTypeGarantie = ["Individuelle", "Portefeuille", "Cautionnement"];

const typeMainleveeOptions = Object.values(TypeMainlevee).map(val => ({
  value: val as string,
  label: (val as string).replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase()).trim()
}));

const fetcher = async (url: string) => {
  const res = await fetch(url);
  if (!res.ok) {
    throw new Error(`HTTP error! status: ${res.status}`);
  }
  return res.json();
};

export default function GarantiesPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [allocationsOptions, setAllocationsOptions] = useState<SelectOption[]>([]);
  const [projetsOptions, setProjetsOptions] = useState<SelectOption[]>([]);

  const [pageStatus, setPageStatus] = useState<"loading" | "loaded" | "error" | "unauthorized">("loading");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingGarantie, setEditingGarantie] = useState<GarantieColumn | null>(null);
  const [filterStatut, setFilterStatut] = useState<string>("");
  const [filterPartenaire, setFilterPartenaire] = useState<string>("");
  const [filterClient, setFilterClient] = useState<string>("");
  const [filterText, setFilterText] = useState<string>("");
  const [selectedGarantie, setSelectedGarantie] = useState<GarantieColumn | null>(null);
  const [page, setPage] = useState(1);
  const pageSize = 10;

  const [garantieToDelete, setGarantieToDelete] = useState<GarantieColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const [isDemandeMainleveeFormOpen, setIsDemandeMainleveeFormOpen] = useState(false);
  const [garantiePourMainlevee, setGarantiePourMainlevee] = useState<GarantieColumn | null>(null);

  const [isDemandeMiseEnJeuFormOpen, setIsDemandeMiseEnJeuFormOpen] = useState(false);
  const [garantiePourMiseEnJeu, setGarantiePourMiseEnJeu] = useState<GarantieColumn | null>(null);

  const [isSubmittingMiseEnJeu, setIsSubmittingMiseEnJeu] = useState(false);

  const isEditing = !!editingGarantie;

  // Deux forms séparés pour éviter les problèmes de typage
  const formCreate = useForm<GarantieFormValues>({ resolver: zodResolver(GarantieSchema) });
  const formEdit = useForm<UpdateGarantieFormValues>({ resolver: zodResolver(UpdateGarantieSchema) });
  const form = isEditing ? formEdit : formCreate;

  const demandeMainleveeForm = useForm<DemandeMainleveeFormValues>({
    resolver: zodResolver(DemandeMainleveeSchema),
    defaultValues: {
      typeMainlevee: undefined,
      dateDemande: new Date(),
      commentairesDemande: "",
    },
  });

  const demandeMiseEnJeuForm = useForm<DemandeMiseEnJeuFormValues>({
    resolver: zodResolver(DemandeMiseEnJeuSchema),
    defaultValues: {
      dateDemande: new Date(),
      montantDemandeStr: "",
      motifDemande: "",
    },
  });

  const { data: garanties = [], error: garantiesError, isLoading: garantiesLoading, mutate } = useSWR("/api/garanties", fetcher, {
    revalidateOnFocus: true,
    dedupingInterval: 10000,
  });

  const [hasLoaded, setHasLoaded] = useState(false);

  const fetchFormData = useCallback(async () => {
    try {
      console.log("[GarantiesPage] Début fetchFormData");
      // Charger les allocations actives avec du disponible
      const allocRes = await fetch("/api/allocations?statut=Active"); // Adapter l'API pour filtrer
      console.log("[GarantiesPage] Statut réponse API allocations:", allocRes.status);
      if (!allocRes.ok) throw new Error("Échec chargement allocations");
      const allocData: any[] = await allocRes.json();
      console.log("[GarantiesPage] Données allocations reçues de l'API:", allocData);
      setAllocationsOptions(
        allocData
          .filter(a => {
            if (!a.ligneGarantie) return false;
            const disponible = a.ligneGarantie.montantDisponible != null
              ? a.ligneGarantie.montantDisponible.toString()
              : "0";
            return new Decimal(disponible).greaterThan(0);
          })
          .map((a, index) => ({
            value: a.id.toString(),
            label: `Alloc. ${a.id} - ${a.partenaire.nom} (Ligne: ${a.ligneGarantie.nom}, Dispo: ${formatCurrency(a.ligneGarantie.montantDisponible, a.ligneGarantie.devise)})`
          }))
      );

      // Charger les projets
      const projetsRes = await fetch("/api/configuration/projets");
      if (!projetsRes.ok) throw new Error("Échec chargement projets");
      const projetsData: any[] = await projetsRes.json();
      setProjetsOptions(projetsData.map((p, index) => ({ value: p.id.toString(), label: `${p.nom} (Client: ${p.clientBeneficiaire.nomOuRaisonSociale})` })));

    } catch (error: any) {
      toast({ title: "Erreur chargement données de formulaire", description: error.message, variant: "destructive" });
    }
  }, [toast]);

  useEffect(() => {
    if (hasLoaded) return;
    if (sessionStatus === "loading") {
      if (pageStatus !== "loading") setPageStatus("loading");
      return;
    }
    if (!session || !ROLES_AUTORISES.includes(session.user?.role as RoleUtilisateur)) {
      if (pageStatus !== "unauthorized") {
        if (sessionStatus !== "unauthenticated") toast({ title: "Accès refusé", variant: "destructive" });
        router.replace("/");
        setPageStatus("unauthorized");
      }
      return;
    }
    setPageStatus("loading");
    Promise.all([fetchFormData()]).then(() => {
      setHasLoaded(true);
      setPageStatus("loaded");
    });
  }, [session, sessionStatus, pageStatus, router, toast, fetchFormData, hasLoaded]);

  const openCreateForm = () => {
    setEditingGarantie(null);
    formCreate.reset({
      allocationId: "",
      projetId: "",
      typeGarantie: typeGarantieOptions[0]?.value ?? "",
      montantCreditStr: "",
      tauxCouvertureAppliqueStr: "",
      dateOctroiCredit: new Date(),
      dateDemandeGarantie: new Date(),
      dateEcheanceInitialeCredit: new Date(),
      dateEcheanceGarantie: undefined,
      statut: StatutGarantie.EnInstruction,
      identifiantCreditPartenaire: "",
      conditionsParticulieres: "",
      delaiMiseEnJeu: "90",
    });
    setIsFormOpen(true);
  };

  const handleEdit = useCallback((garantie: GarantieColumn) => {
    setEditingGarantie(garantie);
    formEdit.reset({
      statut: garantie.statut,
      identifiantCreditPartenaire: garantie.identifiantCreditPartenaire,
      conditionsParticulieres: garantie.conditionsParticulieres || "",
      delaiMiseEnJeu: garantie.delaiMiseEnJeu.toString(),
      dateEcheanceGarantie: garantie.dateEcheanceGarantie ? new Date(garantie.dateEcheanceGarantie) : undefined,
      dateAccordGarantie: garantie.dateAccordGarantie ? new Date(garantie.dateAccordGarantie) : undefined,
      dateEffetGarantie: garantie.dateEffetGarantie ? new Date(garantie.dateEffetGarantie) : undefined,
      dateDernierRemboursementClient: garantie.dateDernierRemboursementClient ? new Date(garantie.dateDernierRemboursementClient) : undefined,
      montantRestantDuCreditStr: garantie.montantRestantDuCredit?.toString().replace('.', ',') || "",
      nombreEcheancesImpayeesStr: garantie.nombreEcheancesImpayees?.toString() || "",
    });
    setIsFormOpen(true);
  }, [formEdit]);

  const handleDelete = useCallback((garantie: GarantieColumn) => {
    setGarantieToDelete(garantie);
    setIsDeleteDialogOpen(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!garantieToDelete) return;
    try {
      const res = await fetch(`/api/garanties/${garantieToDelete.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ statut: StatutGarantie.Supprimee })
      });
      if (!res.ok) throw new Error("Erreur lors de la suppression");
      toast({ title: "Garantie supprimée", description: "La garantie a été supprimée avec succès." });
      setIsDeleteDialogOpen(false);
      setGarantieToDelete(null);
      mutate(
        garanties.map((g: any) => g.id === garantieToDelete.id ? { ...g, statut: StatutGarantie.Supprimee } : g),
        false
      );
    } catch (e: any) {
      toast({ title: "Erreur", description: e.message, variant: "destructive" });
    }
  }, [garantieToDelete, toast, garanties, mutate]);

  const onSubmit = useCallback(async (values: any) => {
    const url = editingGarantie ? `/api/garanties/${editingGarantie.id}` : "/api/garanties";
    const method = editingGarantie ? "PUT" : "POST";
    const dataToSend = { ...values };
    if (dataToSend.montantRestantDuCreditStr === "") dataToSend.montantRestantDuCreditStr = undefined;
    if (dataToSend.nombreEcheancesImpayeesStr === "") dataToSend.nombreEcheancesImpayeesStr = undefined;
    try {
      const response = await fetch(url, { method, headers: { "Content-Type": "application/json" }, body: JSON.stringify(dataToSend) });
      const responseData = await response.json();
      if (!response.ok) {
        if (responseData.errors) {
          Object.keys(responseData.errors).forEach((key) => {
            form.setError(key as any, { type: "server", message: responseData.errors[key].join(', ') });
          });
        }
        throw new Error(responseData.message || "Échec de l'opération");
      }
      toast({ title: editingGarantie ? "Garantie Mise à Jour" : "Garantie Créée" });
      setIsFormOpen(false); setEditingGarantie(null); form.reset();
      mutate(
        garanties.map((g: any) => (editingGarantie && g.id === editingGarantie.id) ? responseData : g),
        false
      );
    } catch (error: any) { toast({ title: "Erreur", description: error.message, variant: "destructive" }); }
  }, [editingGarantie, form, toast, garanties, mutate]);

  const handleOpenDemandeMainleveeForm = useCallback((garantie: GarantieColumn) => {
    setGarantiePourMainlevee(garantie);
    demandeMainleveeForm.reset({ // Réinitialiser avec les valeurs par défaut
      typeMainlevee: undefined, // Laisser l'utilisateur choisir
      dateDemande: new Date(), // Date du jour par défaut
      commentairesDemande: "",
    });
    setIsDemandeMainleveeFormOpen(true);
  }, [demandeMainleveeForm]);

  const onDemandeMainleveeSubmit = useCallback(async (values: DemandeMainleveeFormValues) => {
    if (!garantiePourMainlevee) return;

    try {
      const response = await fetch(`/api/garanties/${garantiePourMainlevee.id}/mainlevees`, { // Utilisation de .id
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });

      const responseData = await response.json();
      if (!response.ok) {
        throw new Error(responseData.message || "Échec de la demande de mainlevée");
      }

      toast({ title: "Demande de Mainlevée Soumise", description: "Votre demande a été enregistrée et est en cours de traitement." });
      setIsDemandeMainleveeFormOpen(false);
      //demandeMainleveeForm.reset(); // Déjà fait dans handleOpenDemandeMainleveeForm ou à la fermeture
      mutate(
        garanties.map((g: any) => g.id === garantiePourMainlevee.id ? { ...g, statut: "MainleveeAccordee" } : g),
        false
      );
    } catch (error: any) {
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    }
  }, [garantiePourMainlevee, toast, garanties, mutate]);

  const handleOpenDemandeMiseEnJeuForm = useCallback((garantie: GarantieColumn) => {
    setGarantiePourMiseEnJeu(garantie);
    demandeMiseEnJeuForm.reset({
      dateDemande: new Date(),
      montantDemandeStr: Number(garantie.montantGarantie).toLocaleString('fr-FR', { 
        minimumFractionDigits: 0, 
        maximumFractionDigits: 2 
      }).replace(/\s/g, ''),
      motifDemande: "",
    });
    setIsDemandeMiseEnJeuFormOpen(true);
  }, [demandeMiseEnJeuForm]);

  const onDemandeMiseEnJeuSubmit = useCallback(async (values: DemandeMiseEnJeuFormValues) => {
    if (!garantiePourMiseEnJeu) return;
    setIsSubmittingMiseEnJeu(true);
    try {
      const response = await fetch(`/api/garanties/${garantiePourMiseEnJeu.id}/mises-en-jeu`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.message || "Échec de la demande de mise en jeu");
      toast({ title: "Succès", description: `Demande de mise en jeu enregistrée pour ${garantiePourMiseEnJeu.referenceGarantie}` });
      setIsDemandeMiseEnJeuFormOpen(false);
      mutate(
        garanties.map((g: any) => g.id === garantiePourMiseEnJeu.id ? { ...g, statut: "MiseEnJeuDemandee" } : g),
        false
      );
    } catch (error: any) {
      toast({ title: "Erreur", description: error?.message || "Erreur inconnue", variant: "destructive" });
    } finally {
      setIsSubmittingMiseEnJeu(false);
    }
  }, [garantiePourMiseEnJeu, toast, garanties, mutate]);

  // Passer la nouvelle fonction aux colonnes
  const columns = useMemo(() => getGarantieColumns({
    onEdit: handleEdit,
    onDelete: handleDelete,
    onDemandeMainlevee: handleOpenDemandeMainleveeForm,
    onDemandeMiseEnJeu: handleOpenDemandeMiseEnJeuForm,
  }), [handleEdit, handleDelete, handleOpenDemandeMainleveeForm, handleOpenDemandeMiseEnJeuForm]);

  // Filtres dynamiques
  const statutOptions = useMemo(() => Array.from(new Set(garanties.map((g: any) => g.statut))).map((s, index) => ({ value: s, label: s })), [garanties]);
  const partenaireOptions = useMemo(() => Array.from(new Set(garanties.map((g: any) => g.allocation.partenaire.nom))).map((n, index) => ({ value: n, label: n })), [garanties]);
  const clientOptions = useMemo(() => Array.from(new Set(garanties.map((g: any) => g.projet.clientBeneficiaire.nomOuRaisonSociale))).map((n, index) => ({ value: n, label: n })), [garanties]);

  const filteredGaranties = useMemo(() => {
    return garanties.filter((g: any) => {
      const matchStatut = filterStatut && filterStatut !== "__all__" ? g.statut === filterStatut : true;
      const matchPartenaire = filterPartenaire && filterPartenaire !== "__all__" ? g.allocation.partenaire.nom === filterPartenaire : true;
      const matchClient = filterClient && filterClient !== "__all__" ? g.projet.clientBeneficiaire.nomOuRaisonSociale === filterClient : true;
      const matchText = filterText
        ? [g.referenceGarantie, g.projet.nom, g.allocation.partenaire.nom, g.projet.clientBeneficiaire.nomOuRaisonSociale]
          .filter(Boolean)
          .join(" ")
          .toLowerCase()
          .includes(filterText.toLowerCase())
        : true;
      return matchStatut && matchPartenaire && matchClient && matchText;
    });
  }, [garanties, filterStatut, filterPartenaire, filterClient, filterText]);

  const paginatedGaranties = useMemo(() => {
    const start = (page - 1) * pageSize;
    return filteredGaranties.slice(start, start + pageSize);
  }, [filteredGaranties, page, pageSize]);
  const totalPages = Math.ceil(filteredGaranties.length / pageSize) || 1;

  // Pour éviter le problème de typage, on prépare une version custom de columns AVANT le render
  const columnsWithActions = useMemo(() => columns.map((col: any) => col.id === "actions" ? {
    ...col,
    cell: ({ row }: any) => {
      const garantie = row.original;
      return (
        <div className="flex space-x-1 justify-end">
          <Button variant="ghost" size="icon" title="Voir Détails" onClick={() => setSelectedGarantie(garantie)}><Eye className="h-4 w-4" /></Button>
          <Button variant="ghost" size="icon" onClick={() => handleEdit(garantie)} title="Modifier (Statut/Suivi)"><Edit className="h-4 w-4" /></Button>
          <Button variant="ghost" size="icon" className="text-red-600 hover:text-red-700" onClick={() => handleDelete(garantie)} title="Radier/Supprimer"><svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M3 6h18M9 6V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2m2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" /></svg></Button>
        </div>
      );
    }
  } : col), [columns, handleEdit]);

  const onSubmitForm = isEditing ? formEdit.handleSubmit(onSubmit) : formCreate.handleSubmit(onSubmit);

  // Détermine si la mainlevée est possible pour la garantie sélectionnée (pour la modale)
  let peutDemanderMainlevee = false;
  if (selectedGarantie) {
    const statutsPermettantDemandeMainlevee = [
      StatutGarantie.Active,
      StatutGarantie.EnSouffrance,
      StatutGarantie.Echue,
      StatutGarantie.MiseEnJeuAcceptee,
      StatutGarantie.MiseEnJeuPayee,
      StatutGarantie.Transferree,
    ];
    peutDemanderMainlevee = statutsPermettantDemandeMainlevee.map(String).includes(String(selectedGarantie.statut))
      && selectedGarantie.statut !== StatutGarantie.MainleveeDemandee
      && selectedGarantie.statut !== StatutGarantie.MainleveeAccordee;
  }

  if (pageStatus === "loading") return <div className="p-6 text-center">Chargement des garanties...</div>;
  // ... (autres vérifications de pageStatus et session) ...

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-2">
        <h1 className="text-3xl font-bold">Gestion des Garanties</h1>
        <Dialog open={isFormOpen} onOpenChange={(open: boolean) => {
          if (!open) {
            setEditingGarantie(null);
            if (isEditing) formEdit.reset();
            else formCreate.reset();
          }
          setIsFormOpen(open);
        }}>
          <DialogTrigger asChild><Button onClick={openCreateForm}><PlusCircle className="mr-2 h-4 w-4" /> Demander/Octroyer Garantie</Button></DialogTrigger>
          <DialogContent className="sm:max-w-2xl lg:max-w-4xl">
            <DialogHeader><DialogTitle>{editingGarantie ? "Modifier" : "Nouvelle"} Garantie</DialogTitle></DialogHeader>
            {isEditing ? (
              <Form {...formEdit}>
                <form onSubmit={onSubmitForm} className="space-y-3 py-4 max-h-[80vh] overflow-y-auto pr-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div><FormLabel>Allocation Partenaire</FormLabel><Input value={editingGarantie?.allocationId?.toString() ?? ''} readOnly disabled /></div>
                    <div><FormLabel>Projet Concerné</FormLabel><Input value={editingGarantie?.projetId?.toString() ?? ''} readOnly disabled /></div>
                    <div><FormLabel>Type de Garantie</FormLabel><Input value={validTypeGarantie.includes(editingGarantie?.typeGarantie) ? editingGarantie?.typeGarantie : "Individuelle"} readOnly disabled /></div>
                    <div><FormLabel>Montant du Crédit</FormLabel><Input value={editingGarantie?.montantCredit?.toString() ?? ''} readOnly disabled /></div>
                    <div><FormLabel>Taux Couverture Appliqué (%)</FormLabel><Input value={editingGarantie?.tauxCouvertureApplique?.toString() ?? ''} readOnly disabled /></div>
                    <div><FormLabel>Date Octroi Crédit</FormLabel><Input value={editingGarantie?.dateOctroiCredit ? new Date(editingGarantie.dateOctroiCredit).toLocaleDateString('fr-FR') : ''} readOnly disabled /></div>
                    <div><FormLabel>Date Demande Garantie</FormLabel><Input value={editingGarantie?.dateDemandeGarantie ? new Date(editingGarantie.dateDemandeGarantie).toLocaleDateString('fr-FR') : ''} readOnly disabled /></div>
                  </div>
                  <FormField control={formEdit.control} name="identifiantCreditPartenaire" render={({ field }) => (<FormItem><FormLabel>Identifiant Crédit Partenaire <span className="text-red-500">*</span></FormLabel><FormControl><Input placeholder="N° dossier crédit chez le partenaire" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={formEdit.control} name="dateEcheanceGarantie" render={({ field }) => (
                      <FormItem className="flex flex-col"><FormLabel>Échéance Garantie <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value ?? undefined} onDateChange={field.onChange} /><FormMessage /></FormItem>
                    )} />
                    <FormField control={formEdit.control} name="statut" render={({ field }) => (
                      <FormItem><FormLabel>Statut <span className="text-red-500">*</span></FormLabel><Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue placeholder="Choisir un statut..." /></SelectTrigger></FormControl><SelectContent>{statutGarantieOptionsAll.map((opt, index) => <SelectItem key={String(opt.value)} value={String(opt.value)}>{String(opt.label)}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>
                    )} />
                  </div>
                  <FormField control={formEdit.control} name="delaiMiseEnJeu" render={({ field }) => (<FormItem><FormLabel>Délai Mise en Jeu (jours) <span className="text-red-500">*</span></FormLabel><FormControl><Input type="number" placeholder="Ex: 90" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={formEdit.control} name="conditionsParticulieres" render={({ field }) => (<FormItem><FormLabel>Conditions Particulières</FormLabel><FormControl><Textarea rows={3} {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <h3 className="text-lg font-medium border-t pt-4 mt-4">Suivi du Crédit</h3>
                  <FormField control={formEdit.control} name="dateDernierRemboursementClient" render={({ field }) => (<FormItem className="flex flex-col"><FormLabel>Date Dernier Remb. Client</FormLabel><DatePicker date={field.value ?? undefined} onDateChange={field.onChange} /><FormMessage /></FormItem>)} />
                  <FormField control={formEdit.control} name="montantRestantDuCreditStr" render={({ field }) => (<FormItem><FormLabel>Montant Restant Dû</FormLabel><FormControl><Input type="text" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <FormField control={formEdit.control} name="nombreEcheancesImpayeesStr" render={({ field }) => (<FormItem><FormLabel>Nb. Échéances Impayées</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <DialogFooter className="pt-4"><DialogClose asChild><Button variant="outline">Annuler</Button></DialogClose><Button type="submit" disabled={formEdit.formState.isSubmitting}>Mettre à jour</Button></DialogFooter>
                </form>
              </Form>
            ) : (
              <Form {...formCreate}>
                <form onSubmit={onSubmitForm} className="space-y-3 py-4 max-h-[80vh] overflow-y-auto pr-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={formCreate.control} name="allocationId" render={({ field }) => (
                      <FormItem><FormLabel>Allocation Partenaire <span className="text-red-500">*</span></FormLabel><Select onValueChange={field.onChange} value={field.value}><FormControl><SelectTrigger><SelectValue placeholder="Choisir une allocation..." /></SelectTrigger></FormControl><SelectContent>{allocationsOptions.length > 0 ? (allocationsOptions.map((opt, index) => <SelectItem key={String(opt.value)} value={String(opt.value)} disabled={opt.disabled}>{String(opt.label)}</SelectItem>)) : (<SelectItem value="no-options" disabled>Aucune allocation disponible</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>
                    )} />
                    <FormField control={formCreate.control} name="projetId" render={({ field }) => (
                      <FormItem><FormLabel>Projet Concerné <span className="text-red-500">*</span></FormLabel><Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue placeholder="Choisir un projet..." /></SelectTrigger></FormControl><SelectContent>{projetsOptions.map((opt, index) => <SelectItem key={String(opt.value)} value={String(opt.value)}>{String(opt.label)}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>
                    )} />
                  </div>
                  <FormField control={formCreate.control} name="typeGarantie" render={({ field }) => (
                    <FormItem><FormLabel>Type de Garantie <span className="text-red-500">*</span></FormLabel><Select onValueChange={field.onChange} value={field.value}><FormControl><SelectTrigger><SelectValue placeholder="Choisir un type..." /></SelectTrigger></FormControl><SelectContent>{typeGarantieOptions.map((opt, index) => <SelectItem key={String(opt.value)} value={String(opt.value)}>{String(opt.label)}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>
                  )} />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={formCreate.control} name="montantCreditStr" render={({ field }) => (
                      <FormItem><FormLabel>Montant du Crédit <span className="text-red-500">*</span></FormLabel><FormControl><Input type="text" placeholder="Ex: 25000000" {...field} /></FormControl><FormMessage /></FormItem>
                    )} />
                    <FormField control={formCreate.control} name="tauxCouvertureAppliqueStr" render={({ field }) => (
                      <FormItem><FormLabel>Taux Couverture Appliqué (%) <span className="text-red-500">*</span></FormLabel><FormControl><Input type="text" placeholder="Ex: 50" {...field} /></FormControl><FormDescription>En pourcentage (ex: 50 pour 50%)</FormDescription><FormMessage /></FormItem>
                    )} />
                  </div>
                  <FormField control={formCreate.control} name="identifiantCreditPartenaire" render={({ field }) => (<FormItem><FormLabel>Identifiant Crédit Partenaire <span className="text-red-500">*</span></FormLabel><FormControl><Input placeholder="N° dossier crédit chez le partenaire" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={formCreate.control} name="dateOctroiCredit" render={({ field }) => (
                      <FormItem className="flex flex-col"><FormLabel>Date Octroi Crédit <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value ?? undefined} onDateChange={field.onChange} /><FormMessage /></FormItem>
                    )} />
                    <FormField control={formCreate.control} name="dateDemandeGarantie" render={({ field }) => (
                      <FormItem className="flex flex-col"><FormLabel>Date Demande Garantie <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value ?? undefined} onDateChange={field.onChange} /><FormMessage /></FormItem>
                    )} />
                    <FormField control={formCreate.control} name="dateEcheanceInitialeCredit" render={({ field }) => (
                      <FormItem className="flex flex-col"><FormLabel>Échéance Initiale Crédit <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value ?? undefined} onDateChange={field.onChange} /><FormMessage /></FormItem>
                    )} />
                    <FormField control={formCreate.control} name="dateEcheanceGarantie" render={({ field }) => (
                      <FormItem className="flex flex-col"><FormLabel>Échéance Garantie <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value ?? undefined} onDateChange={field.onChange} /><FormMessage /></FormItem>
                    )} />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={formCreate.control} name="statut" render={({ field }) => (
                      <FormItem><FormLabel>Statut <span className="text-red-500">*</span></FormLabel><Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue placeholder="Choisir un statut..." /></SelectTrigger></FormControl><SelectContent>{statutGarantieCreationOptions.map((opt, index) => <SelectItem key={String(opt.value)} value={String(opt.value)}>{String(opt.label)}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>
                    )} />
                    <FormField control={formCreate.control} name="delaiMiseEnJeu" render={({ field }) => (<FormItem><FormLabel>Délai Mise en Jeu (jours) <span className="text-red-500">*</span></FormLabel><FormControl><Input type="number" placeholder="Ex: 90" {...field} /></FormControl><FormMessage /></FormItem>)} />
                  </div>
                  <FormField control={formCreate.control} name="conditionsParticulieres" render={({ field }) => (<FormItem><FormLabel>Conditions Particulières</FormLabel><FormControl><Textarea rows={3} {...field} /></FormControl><FormMessage /></FormItem>)} />
                  <DialogFooter className="pt-4"><DialogClose asChild><Button variant="outline">Annuler</Button></DialogClose><Button type="submit" disabled={formCreate.formState.isSubmitting}>Sauvegarder la Demande</Button></DialogFooter>
                </form>
              </Form>
            )}
          </DialogContent>
        </Dialog>
      </div>
      {/* Filtres */}
      <div className="flex flex-col md:flex-row gap-2 mb-4 w-full">
        <div className="w-full md:w-1/4">
          <Input placeholder="Recherche (réf, projet, partenaire, client...)" value={filterText} onChange={e => setFilterText(e.target.value)} />
        </div>
        <div className="w-full md:w-1/4">
          <Select value={filterStatut} onValueChange={v => setFilterStatut(v)}>
            <SelectTrigger><SelectValue placeholder="Filtrer par statut" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="__all__">Tous statuts</SelectItem>
              {statutOptions.map(opt => (
                <SelectItem key={String(opt.value)} value={String(opt.value)}>
                  {String(opt.label)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="w-full md:w-1/4">
          <Select value={filterPartenaire} onValueChange={v => setFilterPartenaire(v)}>
            <SelectTrigger><SelectValue placeholder="Filtrer par partenaire" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="__all__">Tous partenaires</SelectItem>
              {partenaireOptions.map(opt => (
                <SelectItem key={String(opt.value)} value={String(opt.value)}>
                  {String(opt.label)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="w-full md:w-1/4">
          <Select value={filterClient} onValueChange={v => setFilterClient(v)}>
            <SelectTrigger><SelectValue placeholder="Filtrer par client" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="__all__">Tous clients</SelectItem>
              {clientOptions.map(opt => (
                <SelectItem key={String(opt.value)} value={String(opt.value)}>
                  {String(opt.label)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="rounded-md border overflow-x-auto">
        <DataTable columns={columnsWithActions} data={paginatedGaranties} />
      </div>
      {/* Pagination */}
      <div className="flex items-center justify-between px-2 mt-4">
        <div className="text-sm text-muted-foreground">Total: {filteredGaranties.length} garanties</div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={() => setPage(1)} disabled={page === 1}>{'<<'}</Button>
          <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page === 1}>{'<'}</Button>
          <span className="text-sm font-medium">Page {page} sur {totalPages}</span>
          <Button variant="outline" size="sm" onClick={() => setPage(page + 1)} disabled={page === totalPages}>{'>'}</Button>
          <Button variant="outline" size="sm" onClick={() => setPage(totalPages)} disabled={page === totalPages}>{'>>'}</Button>
        </div>
      </div>
      {/* Modale consultation garantie */}
      <Dialog open={!!selectedGarantie} onOpenChange={open => { if (!open) setSelectedGarantie(null); }}>
        <DialogContent className="sm:max-w-2xl lg:max-w-3xl max-h-[80vh] overflow-y-auto pr-3">
          <DialogHeader>
            <DialogTitle>Détail Garantie</DialogTitle>
            <DialogDescription>Consultation des informations détaillées de la garantie.</DialogDescription>
          </DialogHeader>
          {selectedGarantie && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1"><label className="block text-sm font-medium">Réf. Garantie</label><Input value={selectedGarantie.referenceGarantie} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Projet</label><Input value={selectedGarantie.projet.nom} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Client Bénéficiaire</label><Input value={selectedGarantie.projet.clientBeneficiaire.nomOuRaisonSociale} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Partenaire</label><Input value={selectedGarantie.allocation.partenaire.nom} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Type de Garantie</label><Input value={selectedGarantie.typeGarantie} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Statut</label><Input value={selectedGarantie.statut} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Montant Crédit</label><Input value={selectedGarantie.montantCredit?.toString() ?? ''} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Montant Garanti</label><Input value={selectedGarantie.montantGarantie?.toString() ?? ''} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Taux Couverture Appliqué (%)</label><Input value={selectedGarantie.tauxCouvertureApplique?.toString() ?? ''} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Identifiant Crédit Partenaire</label><Input value={selectedGarantie.identifiantCreditPartenaire} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Délai Mise en Jeu (jours)</label><Input value={selectedGarantie.delaiMiseEnJeu} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Date Octroi Crédit</label><Input value={selectedGarantie.dateOctroiCredit ? new Date(selectedGarantie.dateOctroiCredit).toLocaleDateString('fr-FR') : ''} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Date Demande Garantie</label><Input value={selectedGarantie.dateDemandeGarantie ? new Date(selectedGarantie.dateDemandeGarantie).toLocaleDateString('fr-FR') : ''} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Échéance Initiale Crédit</label><Input value={selectedGarantie.dateEcheanceInitialeCredit ? new Date(selectedGarantie.dateEcheanceInitialeCredit).toLocaleDateString('fr-FR') : ''} readOnly /></div>
                <div className="space-y-1"><label className="block text-sm font-medium">Échéance Garantie</label><Input value={selectedGarantie.dateEcheanceGarantie ? new Date(selectedGarantie.dateEcheanceGarantie).toLocaleDateString('fr-FR') : ''} readOnly /></div>
              </div>
              <div className="space-y-1"><label className="block text-sm font-medium">Conditions Particulières</label><Textarea value={selectedGarantie.conditionsParticulieres || "-"} readOnly rows={2} /></div>
              {peutDemanderMainlevee && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-green-600 hover:text-green-700"
                  onClick={() => handleOpenDemandeMainleveeForm(selectedGarantie)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 19c.553 0 1.053-.228 1.414-.586l2.586-2.586a2 2 0 0 0 0-2.828l-7-7a2 2 0 0 0-2.828 0l-2.586 2.586a2 2 0 0 0 0 2.828l7 7A2 2 0 0 0 18 19z" /></svg>
                  Demander Mainlevée
                </Button>
              )}
              <DialogClose asChild><Button variant="outline" className="mt-4">Fermer</Button></DialogClose>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* === NOUVEAU DIALOG POUR LA DEMANDE DE MAINLEVÉE === */}
      {garantiePourMainlevee && (
        <Dialog open={isDemandeMainleveeFormOpen} onOpenChange={(open) => {
          if (!open) setGarantiePourMainlevee(null); // Réinitialiser si on ferme
          setIsDemandeMainleveeFormOpen(open);
        }}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Demander une Mainlevée</DialogTitle>
              <DialogDescription>
                Pour la garantie : <strong>{garantiePourMainlevee.referenceGarantie}</strong> <br />
                Client : {garantiePourMainlevee.projet.clientBeneficiaire.nomOuRaisonSociale}
              </DialogDescription>
            </DialogHeader>
            <Form {...demandeMainleveeForm}>
              <form onSubmit={demandeMainleveeForm.handleSubmit(onDemandeMainleveeSubmit)} className="space-y-4 py-4">
                <FormField
                  control={demandeMainleveeForm.control}
                  name="typeMainlevee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type de Mainlevée <span className="text-red-500">*</span></FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl><SelectTrigger><SelectValue placeholder="Choisir un type..." /></SelectTrigger></FormControl>
                        <SelectContent>
                          {typeMainleveeOptions.map((opt, index) => <SelectItem key={String(opt.value)} value={String(opt.value)}>{String(opt.label)}</SelectItem>)}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={demandeMainleveeForm.control}
                  name="dateDemande"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Date de la Demande <span className="text-red-500">*</span></FormLabel>
                      <DatePicker date={field.value} onDateChange={field.onChange} />
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={demandeMainleveeForm.control}
                  name="commentairesDemande"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Commentaires (Optionnel)</FormLabel>
                      <FormControl><Textarea rows={4} placeholder="Ajoutez des commentaires ou justifications..." {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter className="pt-4">
                  <DialogClose asChild><Button type="button" variant="outline">Annuler</Button></DialogClose>
                  <Button type="submit" disabled={demandeMainleveeForm.formState.isSubmitting}>
                    {demandeMainleveeForm.formState.isSubmitting ? "Envoi en cours..." : "Soumettre la Demande"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}
      {/* === NOUVEAU DIALOG POUR LA DEMANDE DE MISE EN JEU === */}
      {garantiePourMiseEnJeu && (
        <Dialog open={isDemandeMiseEnJeuFormOpen} onOpenChange={(open) => {
            if (!open) setGarantiePourMiseEnJeu(null);
            setIsDemandeMiseEnJeuFormOpen(open);
        }}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Demander une Mise en Jeu</DialogTitle>
              <DialogDescription>
                Pour la garantie : <strong>{garantiePourMiseEnJeu.referenceGarantie}</strong> <br />
                Montant Garanti Actuel : {formatCurrency(garantiePourMiseEnJeu.montantGarantie, garantiePourMiseEnJeu.allocation.ligneGarantie.devise)}
              </DialogDescription>
            </DialogHeader>
            <Form {...demandeMiseEnJeuForm}>
              <form onSubmit={demandeMiseEnJeuForm.handleSubmit(onDemandeMiseEnJeuSubmit)} className="space-y-4 py-4">
                <FormField control={demandeMiseEnJeuForm.control} name="dateDemande" render={({ field }) => (
                    <FormItem className="flex flex-col"><FormLabel>Date de la Demande <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value} onDateChange={field.onChange} /><FormMessage /></FormItem>
                )} />
                <FormField control={demandeMiseEnJeuForm.control} name="montantDemandeStr" render={({ field }) => (
                    <FormItem><FormLabel>Montant Demandé <span className="text-red-500">*</span></FormLabel><FormControl><Input type="text" placeholder="Montant réclamé au Fonds" {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <FormField control={demandeMiseEnJeuForm.control} name="motifDemande" render={({ field }) => (
                    <FormItem><FormLabel>Motif de la Demande <span className="text-red-500">*</span></FormLabel><FormControl><Textarea rows={4} placeholder="Expliquez la situation de défaut et la raison de la mise en jeu..." {...field} /></FormControl><FormMessage /></FormItem>
                )} />
                <DialogFooter className="pt-4">
                  <DialogClose asChild><Button type="button" variant="outline">Annuler</Button></DialogClose>
                  <Button type="submit" disabled={demandeMiseEnJeuForm.formState.isSubmitting || isSubmittingMiseEnJeu}>
                    {isSubmittingMiseEnJeu ? (
                      <svg className="animate-spin h-4 w-4 mr-2 inline" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>
                    ) : null}
                    {isSubmittingMiseEnJeu ? "Envoi en cours..." : "Soumettre la Demande"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}
      {/* AlertDialog suppression logique */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmation de suppression</AlertDialogTitle>
          </AlertDialogHeader>
          <div>Voulez-vous vraiment supprimer cette garantie ? Cette action est irréversible.</div>
          <AlertDialogFooter>
            <AlertDialogCancel asChild><Button variant="outline">Annuler</Button></AlertDialogCancel>
            <AlertDialogAction asChild><Button variant="default" className="bg-red-600 hover:bg-red-700 text-white" onClick={confirmDelete}>Confirmer la suppression</Button></AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

function ListeMisesEnJeu({ garantieId }: { garantieId: number }) {
  const { data, error, isLoading } = useSWR(garantieId ? `/api/garanties/${garantieId}/mises-en-jeu` : null, fetcher);
  if (!garantieId) return null;
  if (isLoading) return <div>Chargement des mises en jeu...</div>;
  if (error) return <div>Erreur de chargement</div>;
  if (!data?.length) return <div>Aucune mise en jeu</div>;
  return (
    <div className="mt-4">
      <h4 className="font-semibold mb-2">Historique des mises en jeu</h4>
      <ul className="space-y-1">
        {data.map((mj: any, index: number) => (
          <li key={String(mj.id) || String(index)} className="border rounded p-2 text-sm">
            <span className="font-mono">{new Date(mj.dateDemande).toLocaleDateString("fr-FR")}</span>
            {" — "}
            <span>{mj.motifDemande}</span>
            {" — "}
            <span className="font-bold">{mj.montantDemande} XOF</span>
            {" — "}
            <span className="italic">{mj.statut}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}