{"name": "gesgar", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json}\" \"prisma/**/*.prisma\"", "generate:prisma-enums": "tsc scripts/generate-prisma-enums.ts && node scripts/generate-prisma-enums.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.7.0", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-table": "^8.21.3", "bcryptjs": "^3.0.2", "decimal.js": "^10.5.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "sonner": "^2.0.3", "sql-template-strings": "^2.2.2", "swr": "^2.3.3", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/bcryptjs": "^2.4.6", "@types/decimal.js": "^0.0.32", "@types/node": "^20.17.47", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.3.2", "lucide-react": "^0.510.0", "postcss": "^8.5.3", "prettier": "^3.2.5", "prisma": "^6.7.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "ts-node": "^10.9.2", "tw-animate-css": "^1.2.9", "typescript": "^5.8.3"}, "prisma": {"seed": "ts-node --project tsconfig.seed.json prisma/seed.ts"}}