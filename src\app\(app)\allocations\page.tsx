// src/app/(app)/allocations/page.tsx
"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { StatutLigneGarantie } from "@/types/statut-ligne-garantie";
import { getAllocationColumns, AllocationColumn, formatCurrency } from "./columns";
import { DataTable } from "@/components/shared/data-table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { AllocationLignePartenaireFormValues, AllocationLignePartenaireSchema } from "@/lib/schemas/allocation-ligne-partenaire.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { PlusCircle } from "lucide-react";
import Decimal from "decimal.js";

// Enums front minimalistes (adapter selon besoin réel)
export enum StatutAllocation {
  Active = "Active",
  Epuisee = "Epuisee",
  Suspendue = "Suspendue",
  Expiree = "Expiree",
  Cloturee = "Cloturee",
  Renouvelee = "Renouvelee",
  Reaffectee = "Reaffectee",
  EnAttenteValidation = "EnAttenteValidation",
}

export enum Periodicite {
  Mensuelle = "Mensuelle",
  Trimestrielle = "Trimestrielle",
  Semestrielle = "Semestrielle",
  Annuelle = "Annuelle",
  Unique = "Unique",
}

export enum RoleUtilisateur {
  Administrateur = "Administrateur",
  GestionnaireGesGar = "GestionnaireGesGar",
  AnalysteFinancier = "AnalysteFinancier",
  Partenaire = "Partenaire",
  // ... autres rôles si besoin
}

// Types minimalistes pour le front (adapter selon besoin)
type Partenaire = { id: number; nom: string };
type LigneGarantie = { id: number; nom: string; devise: string; montantDisponible: number };
type AllocationLignePartenaire = {
  id: number;
  ligneGarantieId: number;
  partenaireId: number;
  montantAlloue: number;
  montantDisponible: number;
  statut: StatutAllocation;
  dateAllocation: string | Date;
  dateExpiration?: string | Date;
  // Add other essential fields as needed
};

type SelectOption = { value: string; label: string; disabled?: boolean; detail?: string };
const statutAllocationOptions = Object.values(StatutAllocation).map(val => {
  const v = val as string;
  return {
    value: v,
    label: v.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase()).trim()
  };
});
const periodiciteOptions = Object.values(Periodicite).map(val => {
  const v = val as string;
  return {
    value: v,
    label: v.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase()).trim()
  };
});

export default function AllocationsPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { toast } = useToast();

  const [allocations, setAllocations] = useState<AllocationColumn[]>([]);
  const [lignesGarantieOptions, setLignesGarantieOptions] = useState<SelectOption[]>([]);
  const [partenairesOptions, setPartenairesOptions] = useState<SelectOption[]>([]);

  const [pageStatus, setPageStatus] = useState<"idle"|"loading"|"loaded"|"error"|"unauthorized">("idle");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAllocation, setEditingAllocation] = useState<AllocationColumn | null>(null);
  const [allocationToDelete, setAllocationToDelete] = useState<AllocationColumn | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);

  const form = useForm<AllocationLignePartenaireFormValues>({
    resolver: zodResolver(AllocationLignePartenaireSchema) as any,
    defaultValues: {
      ligneGarantieId: "", partenaireId: "", referenceConvention: "", montantAlloueStr: "",
      dateAllocation: new Date(), dateExpiration: undefined, statut: StatutAllocation.EnAttenteValidation,
      tauxCouvertureMaxStr: "", tauxInteretStr: "", tauxCommissionStr: "",
      periodicitePaiementInteret: undefined, periodicitePaiementCommission: undefined, commentaires: "",
    },
  });

  const rolesAutorises: RoleUtilisateur[] = [
    RoleUtilisateur.Administrateur,
    RoleUtilisateur.GestionnaireGesGar,
    RoleUtilisateur.Partenaire,
  ];

  const fetchFormData = useCallback(async () => {
    try {
      const [lignesRes, partenairesRes] = await Promise.all([
        fetch("/api/lignes-garantie?statut=Active,EnAttenteValidation,Epuisee"), // API à adapter pour filtrer par statut
        fetch("/api/configuration/partenaires")
      ]);

      if (!lignesRes.ok) throw new Error("Échec chargement lignes de garantie");
      const lignesData: any[] = await lignesRes.json();
      setLignesGarantieOptions(lignesData
        .filter(lg => new Decimal(lg.montantDisponible?.toString() ?? "0").greaterThan(0) || lg.statut === StatutLigneGarantie.EnAttenteValidation)
        .map(lg => ({ value: lg.id.toString(), label: `${lg.nom} (Dispo: ${formatCurrency(lg.montantDisponible.toString(), lg.devise)})`, detail: lg.devise }))
      );

      if (!partenairesRes.ok) throw new Error("Échec chargement partenaires");
      const partenairesData: Partenaire[] = await partenairesRes.json();
      setPartenairesOptions(partenairesData.map(p => ({ value: p.id.toString(), label: p.nom })));

    } catch (error: any) {
      toast({ title: "Erreur chargement données de formulaire", description: error.message, variant: "destructive" });
    }
  }, []); // Removed toast dependency to prevent infinite re-renders
  
  const fetchAllocationsData = useCallback(async (showLoading = true) => {
    if (showLoading) setPageStatus("loading");
     try {
         const response = await fetch("/api/allocations");
         if (!response.ok) throw new Error("Échec de la récupération des allocations");
         const data = await response.json();
         setAllocations(data);
         if (showLoading) setPageStatus("loaded");
     } catch (error: any) {
         toast({ title: "Erreur", description: error.message, variant: "destructive" });
         if (showLoading) setPageStatus("error");
     }
  }, []); // Removed toast dependency to prevent infinite re-renders

  useEffect(() => {
    if (hasLoaded) return;
    if (sessionStatus === "loading") { setPageStatus("loading"); return; }
    if (!session || !rolesAutorises.includes(session.user?.role as RoleUtilisateur)) {
      if (sessionStatus !== "unauthenticated") toast({ title: "Accès refusé", variant: "destructive" });
      router.replace("/"); setPageStatus("unauthorized"); return;
    }
    setPageStatus("loading");
    Promise.all([fetchFormData(), fetchAllocationsData()]).then(() => {
      setHasLoaded(true);
    });
  }, [session, sessionStatus, hasLoaded, fetchFormData, fetchAllocationsData]); // Removed router and toast dependencies

  const refreshData = useCallback(async () => {
    await Promise.all([fetchFormData(), fetchAllocationsData(false)]);
  }, [fetchFormData, fetchAllocationsData]);

  const handleEdit = useCallback((allocation: AllocationColumn) => {
    setEditingAllocation(allocation);
    form.reset({
      ligneGarantieId: allocation.ligneGarantieId.toString(),
      partenaireId: allocation.partenaireId.toString(),
      referenceConvention: allocation.referenceConvention || "",
      montantAlloueStr: allocation.montantAlloue.toString().replace('.',','),
      dateAllocation: new Date(allocation.dateAllocation),
      dateExpiration: allocation.dateExpiration ? new Date(allocation.dateExpiration) : undefined,
      tauxCouvertureMaxStr: allocation.tauxCouvertureMax?.toString() || "",
      tauxInteretStr: allocation.tauxInteret?.toString() || "",
      tauxCommissionStr: allocation.tauxCommission?.toString() || "",
      periodicitePaiementInteret: allocation.periodicitePaiementInteret || undefined,
      periodicitePaiementCommission: allocation.periodicitePaiementCommission || undefined,
      commentaires: allocation.commentaires || "",
    });
    setIsFormOpen(true);
  }, [form]);

const handleDeleteConfirm = useCallback((allocation: AllocationColumn) => {
  setAllocationToDelete(allocation);
  setIsDeleteDialogOpen(true);
}, []);

const handleDelete = useCallback(async () => {
  if (!allocationToDelete) return;
  
  try {
    const response = await fetch(`/api/allocations/${allocationToDelete.id}`, {
      method: "DELETE",
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erreur lors de la suppression");
    }
    
    toast({ title: "Allocation supprimée" });
    setIsDeleteDialogOpen(false);
    setAllocationToDelete(null);
    await refreshData();
  } catch (error: any) {
    toast({ title: "Erreur", description: error.message, variant: "destructive" });
  }
}, [allocationToDelete, toast, refreshData]);
  const onSubmit = useCallback(async (values: AllocationLignePartenaireFormValues) => {
    const url = editingAllocation ? `/api/allocations/${editingAllocation.id}` : "/api/allocations";
    const method = editingAllocation ? "PUT" : "POST";
    try {
      const response = await fetch(url, { method, headers: { "Content-Type": "application/json" }, body: JSON.stringify(values) });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Erreur lors de ${editingAllocation ? "la modification" : "la création"} de l'allocation`);
      }
      toast({ title: editingAllocation ? "Allocation Mise à Jour" : "Allocation Créée" });
      setIsFormOpen(false); setEditingAllocation(null); form.reset(); await refreshData();
    } catch (error: any) { toast({ title: "Erreur", description: error.message, variant: "destructive" }); }
  }, [editingAllocation, form, toast, refreshData]);

  const columns = useMemo(() => getAllocationColumns({ onEdit: handleEdit, onDelete: handleDeleteConfirm }), [handleEdit, handleDeleteConfirm]);


  if (pageStatus === "loading") return <div className="p-6 text-center">Chargement des allocations...</div>;

  return (
    <div className="container mx-auto py-10 px-4 md:px-0">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Allocations aux Partenaires</h1>
        <Dialog open={isFormOpen} onOpenChange={(open) => { if(!open){setEditingAllocation(null); form.reset();} setIsFormOpen(open);}}>
          <DialogTrigger asChild><Button onClick={() => { /* ... */ }}><PlusCircle /> Ajouter Allocation</Button></DialogTrigger>
          <DialogContent className="sm:max-w-2xl lg:max-w-3xl">
            <DialogHeader><DialogTitle>{editingAllocation ? "Modifier" : "Ajouter"} une Allocation</DialogTitle></DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3 py-4 max-h-[80vh] overflow-y-auto pr-3">
                {/* --- Champs du Formulaire --- */}
                <FormField control={form.control} name="ligneGarantieId" render={({ field }) => (
                    <FormItem><FormLabel>Ligne de Garantie <span className="text-red-500">*</span></FormLabel>
                        <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Choisir une ligne..." /></SelectTrigger></FormControl>
                            <SelectContent>{lignesGarantieOptions.map(opt => <SelectItem key={opt.value} value={opt.value} disabled={opt.disabled}>{opt.label}</SelectItem>)}</SelectContent>
                        </Select><FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="partenaireId" render={({ field }) => (
                    <FormItem><FormLabel>Partenaire <span className="text-red-500">*</span></FormLabel>
                        <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Choisir un partenaire..." /></SelectTrigger></FormControl>
                            <SelectContent>{partenairesOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
                        </Select><FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="montantAlloueStr" render={({ field }) => ( <FormItem><FormLabel>Montant Alloué <span className="text-red-500">*</span></FormLabel><FormControl><Input type="text" placeholder="Ex: 50000000" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="dateAllocation" render={({ field }) => ( <FormItem className="flex flex-col"><FormLabel>Date d&apos;Allocation <span className="text-red-500">*</span></FormLabel><DatePicker date={field.value || undefined} onDateChange={field.onChange} /><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="dateExpiration" render={({ field }) => ( <FormItem className="flex flex-col"><FormLabel>Date d&apos;Expiration (Optionnel)</FormLabel><DatePicker date={field.value || undefined} onDateChange={field.onChange} /><FormMessage /></FormItem> )}/>
                </div>
                <FormField control={form.control} name="statut" render={({ field }) => (
                    <FormItem><FormLabel>Statut <span className="text-red-500">*</span></FormLabel>
                        <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Choisir un statut..." /></SelectTrigger></FormControl>
                            <SelectContent>{statutAllocationOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
                        </Select><FormMessage />
                    </FormItem>
                )} />
                <FormField control={form.control} name="referenceConvention" render={({ field }) => ( <FormItem><FormLabel>Réf. Convention Allocation</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem> )}/>

                <h3 className="text-lg font-medium border-t pt-4 mt-4">Conditions Financières</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField control={form.control} name="tauxCouvertureMaxStr" render={({ field }) => ( <FormItem><FormLabel>Taux Couverture Max (%)</FormLabel><FormControl><Input type="text" placeholder="Ex: 75" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="tauxInteretStr" render={({ field }) => ( <FormItem><FormLabel>Taux Intérêt Annuel (%)</FormLabel><FormControl><Input type="text" placeholder="Ex: 5" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="tauxCommissionStr" render={({ field }) => ( <FormItem><FormLabel>Taux Commission (%)</FormLabel><FormControl><Input type="text" placeholder="Ex: 1.5" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                </div>
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="periodicitePaiementInteret" render={({ field }) => (
                        <FormItem><FormLabel>Périodicité Intérêts</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value || "aucune"} defaultValue={field.value || "aucune"}>
                                <FormControl><SelectTrigger><SelectValue placeholder="Choisir..." /></SelectTrigger></FormControl>
                                <SelectContent>
                                    <SelectItem value="aucune">Aucune</SelectItem>
                                    {periodiciteOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
                                </SelectContent>
                            </Select><FormMessage />
                        </FormItem>
                    )} />
                    <FormField control={form.control} name="periodicitePaiementCommission" render={({ field }) => (
                         <FormItem><FormLabel>Périodicité Commissions</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value || "aucune"} defaultValue={field.value || "aucune"}>
                                <FormControl><SelectTrigger><SelectValue placeholder="Choisir..." /></SelectTrigger></FormControl>
                                <SelectContent>
                                    <SelectItem value="aucune">Aucune</SelectItem>
                                    {periodiciteOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
                                </SelectContent>
                            </Select><FormMessage />
                        </FormItem>
                    )} />
                </div>
                <FormField control={form.control} name="commentaires" render={({ field }) => ( <FormItem><FormLabel>Commentaires</FormLabel><FormControl><Textarea rows={3} {...field} /></FormControl><FormMessage /></FormItem> )}/>
                <DialogFooter className="pt-4"><DialogClose asChild><Button variant="outline">Annuler</Button></DialogClose><Button type="submit" disabled={form.formState.isSubmitting}>Sauvegarder</Button></DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      <DataTable columns={columns} data={allocations} />
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
  <AlertDialogContent>
    <AlertDialogHeader>
      <AlertDialogTitle>Confirmer la suppression</AlertDialogTitle>
      <AlertDialogDescription>
        Êtes-vous sûr de vouloir supprimer cette allocation? Cette action ne peut pas être annulée.
      </AlertDialogDescription>
    </AlertDialogHeader>
    <AlertDialogFooter>
      <AlertDialogCancel>Annuler</AlertDialogCancel>
      <AlertDialogAction onClick={handleDelete}>Supprimer</AlertDialogAction>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>
    </div>
  );
}              