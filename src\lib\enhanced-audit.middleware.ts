// src/lib/enhanced-audit.middleware.ts

if (typeof window !== 'undefined') {
  throw new Error("enhanced-audit.middleware ne doit jamais être importé côté client !");
}

import { Prisma } from '@prisma/client';
import { AsyncLocalStorage } from 'async_hooks';

// Contexte d'audit étendu avec plus d'informations
export interface AuditContext {
  userId?: number;
  ip?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
  module?: string; // Module fonctionnel (ex: "GARANTIES", "ALLOCATIONS", etc.)
  operation?: string; // Opération métier (ex: "CREATION_GARANTIE", "VALIDATION_MISE_EN_JEU")
  resourceId?: string; // ID de la ressource concernée
  metadata?: Record<string, any>; // Métadonnées supplémentaires
}

export const enhancedAuditContext = new AsyncLocalStorage<AuditContext>();

// Liste complète des modèles à auditer - TOUS les modèles métier
const AUDITED_MODELS: Prisma.ModelName[] = [
  // Gestion des utilisateurs et sécurité
  'Utilisateur',
  
  // Configuration de base
  'Bailleur',
  'Partenaire', 
  'ClientBeneficiaire',
  'SecteurActivite',
  'Projet',
  'ParametreSysteme',
  
  // Gestion des garanties
  'LigneGarantie',
  'Garantie',
  'AllocationLignePartenaire',
  
  // Avenants
  'AvenantLigneGarantie',
  'AvenantAllocation',
  
  // Processus métier
  'Mainlevee',
  'MiseEnJeu',
  'TransfertGarantie',
  'PaiementInteretCommission',
  
  // Règles et éligibilité
  'RegleEligibilite',
  'GroupeRegleEligibilite',
  
  // Documents
  'Document'
];

// Actions d'écriture étendues
const WRITE_ACTIONS: Prisma.PrismaAction[] = [
  'create', 'createMany', 'update', 'updateMany', 'upsert', 'delete', 'deleteMany'
];

// Actions de lecture sensibles à auditer
const SENSITIVE_READ_ACTIONS: Prisma.PrismaAction[] = [
  'findFirst', 'findMany', 'findUnique'
];

// Modèles sensibles pour lesquels on veut auditer même les lectures
const SENSITIVE_MODELS: Prisma.ModelName[] = [
  'Utilisateur',
  'AuditLog',
  'ParametreSysteme',
  'Garantie',
  'MiseEnJeu',
  'Mainlevee',
  'PaiementInteretCommission'
];

// Types d'actions métier pour une meilleure traçabilité
export enum BusinessAction {
  // Authentification et autorisation
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  LOGIN_FAILED = 'LOGIN_FAILED',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  
  // Gestion des utilisateurs
  USER_CREATE = 'USER_CREATE',
  USER_UPDATE = 'USER_UPDATE',
  USER_DEACTIVATE = 'USER_DEACTIVATE',
  USER_ACTIVATE = 'USER_ACTIVATE',
  USER_DELETE = 'USER_DELETE',
  ROLE_CHANGE = 'ROLE_CHANGE',
  
  // Configuration système
  SYSTEM_SETTING_CREATE = 'SYSTEM_SETTING_CREATE',
  SYSTEM_SETTING_UPDATE = 'SYSTEM_SETTING_UPDATE',
  SYSTEM_SETTING_DELETE = 'SYSTEM_SETTING_DELETE',
  
  // Gestion des entités de configuration
  BAILLEUR_CREATE = 'BAILLEUR_CREATE',
  BAILLEUR_UPDATE = 'BAILLEUR_UPDATE',
  BAILLEUR_DELETE = 'BAILLEUR_DELETE',
  
  PARTENAIRE_CREATE = 'PARTENAIRE_CREATE',
  PARTENAIRE_UPDATE = 'PARTENAIRE_UPDATE',
  PARTENAIRE_DELETE = 'PARTENAIRE_DELETE',
  
  CLIENT_CREATE = 'CLIENT_CREATE',
  CLIENT_UPDATE = 'CLIENT_UPDATE',
  CLIENT_DELETE = 'CLIENT_DELETE',
  
  SECTEUR_CREATE = 'SECTEUR_CREATE',
  SECTEUR_UPDATE = 'SECTEUR_UPDATE',
  SECTEUR_DELETE = 'SECTEUR_DELETE',
  
  PROJET_CREATE = 'PROJET_CREATE',
  PROJET_UPDATE = 'PROJET_UPDATE',
  PROJET_DELETE = 'PROJET_DELETE',
  
  // Gestion des lignes de garantie
  LIGNE_GARANTIE_CREATE = 'LIGNE_GARANTIE_CREATE',
  LIGNE_GARANTIE_UPDATE = 'LIGNE_GARANTIE_UPDATE',
  LIGNE_GARANTIE_DELETE = 'LIGNE_GARANTIE_DELETE',
  LIGNE_GARANTIE_ACTIVATE = 'LIGNE_GARANTIE_ACTIVATE',
  LIGNE_GARANTIE_SUSPEND = 'LIGNE_GARANTIE_SUSPEND',
  LIGNE_GARANTIE_CLOSE = 'LIGNE_GARANTIE_CLOSE',
  
  // Avenants ligne de garantie
  AVENANT_LIGNE_CREATE = 'AVENANT_LIGNE_CREATE',
  AVENANT_LIGNE_UPDATE = 'AVENANT_LIGNE_UPDATE',
  AVENANT_LIGNE_DELETE = 'AVENANT_LIGNE_DELETE',
  AVENANT_LIGNE_VALIDATE = 'AVENANT_LIGNE_VALIDATE',
  AVENANT_LIGNE_REJECT = 'AVENANT_LIGNE_REJECT',
  
  // Gestion des allocations
  ALLOCATION_CREATE = 'ALLOCATION_CREATE',
  ALLOCATION_UPDATE = 'ALLOCATION_UPDATE',
  ALLOCATION_DELETE = 'ALLOCATION_DELETE',
  ALLOCATION_ACTIVATE = 'ALLOCATION_ACTIVATE',
  ALLOCATION_SUSPEND = 'ALLOCATION_SUSPEND',
  ALLOCATION_CLOSE = 'ALLOCATION_CLOSE',
  
  // Avenants allocation
  AVENANT_ALLOCATION_CREATE = 'AVENANT_ALLOCATION_CREATE',
  AVENANT_ALLOCATION_UPDATE = 'AVENANT_ALLOCATION_UPDATE',
  AVENANT_ALLOCATION_DELETE = 'AVENANT_ALLOCATION_DELETE',
  AVENANT_ALLOCATION_VALIDATE = 'AVENANT_ALLOCATION_VALIDATE',
  AVENANT_ALLOCATION_REJECT = 'AVENANT_ALLOCATION_REJECT',
  
  // Gestion des garanties
  GARANTIE_CREATE = 'GARANTIE_CREATE',
  GARANTIE_UPDATE = 'GARANTIE_UPDATE',
  GARANTIE_DELETE = 'GARANTIE_DELETE',
  GARANTIE_ACTIVATE = 'GARANTIE_ACTIVATE',
  GARANTIE_SUSPEND = 'GARANTIE_SUSPEND',
  GARANTIE_EXPIRE = 'GARANTIE_EXPIRE',
  
  // Processus de mainlevée
  MAINLEVEE_REQUEST = 'MAINLEVEE_REQUEST',
  MAINLEVEE_APPROVE = 'MAINLEVEE_APPROVE',
  MAINLEVEE_REJECT = 'MAINLEVEE_REJECT',
  MAINLEVEE_CANCEL = 'MAINLEVEE_CANCEL',
  MAINLEVEE_EXECUTE = 'MAINLEVEE_EXECUTE',
  
  // Processus de mise en jeu
  MISE_EN_JEU_REQUEST = 'MISE_EN_JEU_REQUEST',
  MISE_EN_JEU_APPROVE = 'MISE_EN_JEU_APPROVE',
  MISE_EN_JEU_REJECT = 'MISE_EN_JEU_REJECT',
  MISE_EN_JEU_CANCEL = 'MISE_EN_JEU_CANCEL',
  MISE_EN_JEU_PAYMENT = 'MISE_EN_JEU_PAYMENT',
  
  // Transferts
  TRANSFERT_CREATE = 'TRANSFERT_CREATE',
  TRANSFERT_APPROVE = 'TRANSFERT_APPROVE',
  TRANSFERT_REJECT = 'TRANSFERT_REJECT',
  TRANSFERT_EXECUTE = 'TRANSFERT_EXECUTE',
  
  // Paiements
  PAIEMENT_CREATE = 'PAIEMENT_CREATE',
  PAIEMENT_UPDATE = 'PAIEMENT_UPDATE',
  PAIEMENT_VALIDATE = 'PAIEMENT_VALIDATE',
  PAIEMENT_CANCEL = 'PAIEMENT_CANCEL',
  
  // Documents
  DOCUMENT_UPLOAD = 'DOCUMENT_UPLOAD',
  DOCUMENT_DOWNLOAD = 'DOCUMENT_DOWNLOAD',
  DOCUMENT_DELETE = 'DOCUMENT_DELETE',
  DOCUMENT_VIEW = 'DOCUMENT_VIEW',
  
  // Imports/Exports
  DATA_IMPORT = 'DATA_IMPORT',
  DATA_EXPORT = 'DATA_EXPORT',
  BULK_UPDATE = 'BULK_UPDATE',
  BULK_DELETE = 'BULK_DELETE',
  
  // Accès aux données sensibles
  SENSITIVE_DATA_ACCESS = 'SENSITIVE_DATA_ACCESS',
  REPORT_GENERATION = 'REPORT_GENERATION',
  AUDIT_LOG_ACCESS = 'AUDIT_LOG_ACCESS',
  
  // Erreurs et tentatives d'accès non autorisé
  UNAUTHORIZED_ACCESS_ATTEMPT = 'UNAUTHORIZED_ACCESS_ATTEMPT',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  DATA_VALIDATION_ERROR = 'DATA_VALIDATION_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR'
}

// Fonction pour déterminer l'action métier basée sur le modèle et l'action Prisma
function getBusinessAction(model: string, action: string, data?: any): BusinessAction {
  const modelUpper = model.toUpperCase();
  const actionUpper = action.toUpperCase();
  
  // Gestion spéciale pour les désactivations
  if (actionUpper === 'UPDATE' && data?.estActif === false) {
    switch (modelUpper) {
      case 'UTILISATEUR': return BusinessAction.USER_DEACTIVATE;
      case 'LIGNE_GARANTIE': return BusinessAction.LIGNE_GARANTIE_SUSPEND;
      case 'ALLOCATION': return BusinessAction.ALLOCATION_SUSPEND;
      case 'GARANTIE': return BusinessAction.GARANTIE_SUSPEND;
      default: return `${modelUpper}_DEACTIVATE` as BusinessAction;
    }
  }
  
  // Mapping standard
  const mapping: Record<string, Record<string, BusinessAction>> = {
    'UTILISATEUR': {
      'CREATE': BusinessAction.USER_CREATE,
      'UPDATE': BusinessAction.USER_UPDATE,
      'DELETE': BusinessAction.USER_DELETE
    },
    'PARAMETRESYSTEME': {
      'CREATE': BusinessAction.SYSTEM_SETTING_CREATE,
      'UPDATE': BusinessAction.SYSTEM_SETTING_UPDATE,
      'DELETE': BusinessAction.SYSTEM_SETTING_DELETE
    },
    'BAILLEUR': {
      'CREATE': BusinessAction.BAILLEUR_CREATE,
      'UPDATE': BusinessAction.BAILLEUR_UPDATE,
      'DELETE': BusinessAction.BAILLEUR_DELETE
    },
    'PARTENAIRE': {
      'CREATE': BusinessAction.PARTENAIRE_CREATE,
      'UPDATE': BusinessAction.PARTENAIRE_UPDATE,
      'DELETE': BusinessAction.PARTENAIRE_DELETE
    },
    'CLIENTBENEFICIAIRE': {
      'CREATE': BusinessAction.CLIENT_CREATE,
      'UPDATE': BusinessAction.CLIENT_UPDATE,
      'DELETE': BusinessAction.CLIENT_DELETE
    },
    'SECTEURACTIVITE': {
      'CREATE': BusinessAction.SECTEUR_CREATE,
      'UPDATE': BusinessAction.SECTEUR_UPDATE,
      'DELETE': BusinessAction.SECTEUR_DELETE
    },
    'PROJET': {
      'CREATE': BusinessAction.PROJET_CREATE,
      'UPDATE': BusinessAction.PROJET_UPDATE,
      'DELETE': BusinessAction.PROJET_DELETE
    },
    'LIGNEGARANTIE': {
      'CREATE': BusinessAction.LIGNE_GARANTIE_CREATE,
      'UPDATE': BusinessAction.LIGNE_GARANTIE_UPDATE,
      'DELETE': BusinessAction.LIGNE_GARANTIE_DELETE
    },
    'AVENANTLIGNEGARANTIE': {
      'CREATE': BusinessAction.AVENANT_LIGNE_CREATE,
      'UPDATE': BusinessAction.AVENANT_LIGNE_UPDATE,
      'DELETE': BusinessAction.AVENANT_LIGNE_DELETE
    },
    'ALLOCATIONLIGNEPARTENAIRE': {
      'CREATE': BusinessAction.ALLOCATION_CREATE,
      'UPDATE': BusinessAction.ALLOCATION_UPDATE,
      'DELETE': BusinessAction.ALLOCATION_DELETE
    },
    'AVENANTALLOCATION': {
      'CREATE': BusinessAction.AVENANT_ALLOCATION_CREATE,
      'UPDATE': BusinessAction.AVENANT_ALLOCATION_UPDATE,
      'DELETE': BusinessAction.AVENANT_ALLOCATION_DELETE
    },
    'GARANTIE': {
      'CREATE': BusinessAction.GARANTIE_CREATE,
      'UPDATE': BusinessAction.GARANTIE_UPDATE,
      'DELETE': BusinessAction.GARANTIE_DELETE
    },
    'MAINLEVEE': {
      'CREATE': BusinessAction.MAINLEVEE_REQUEST,
      'UPDATE': BusinessAction.MAINLEVEE_APPROVE,
      'DELETE': BusinessAction.MAINLEVEE_CANCEL
    },
    'MISEENJEU': {
      'CREATE': BusinessAction.MISE_EN_JEU_REQUEST,
      'UPDATE': BusinessAction.MISE_EN_JEU_APPROVE,
      'DELETE': BusinessAction.MISE_EN_JEU_CANCEL
    },
    'TRANSFERTGARANTIE': {
      'CREATE': BusinessAction.TRANSFERT_CREATE,
      'UPDATE': BusinessAction.TRANSFERT_APPROVE,
      'DELETE': BusinessAction.TRANSFERT_REJECT
    },
    'PAIEMENTINTERETCOMMISSION': {
      'CREATE': BusinessAction.PAIEMENT_CREATE,
      'UPDATE': BusinessAction.PAIEMENT_UPDATE,
      'DELETE': BusinessAction.PAIEMENT_CANCEL
    },
    'DOCUMENT': {
      'CREATE': BusinessAction.DOCUMENT_UPLOAD,
      'UPDATE': BusinessAction.DOCUMENT_UPLOAD,
      'DELETE': BusinessAction.DOCUMENT_DELETE
    }
  };
  
  return mapping[modelUpper]?.[actionUpper] || `${modelUpper}_${actionUpper}` as BusinessAction;
}

// Fonction pour déterminer le niveau de criticité
function getCriticalityLevel(model: string, action: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  const criticalModels = ['Utilisateur', 'ParametreSysteme', 'AuditLog'];
  const highRiskModels = ['MiseEnJeu', 'Mainlevee', 'PaiementInteretCommission', 'TransfertGarantie'];
  const mediumRiskModels = ['Garantie', 'AllocationLignePartenaire', 'LigneGarantie'];
  
  if (criticalModels.includes(model)) return 'CRITICAL';
  if (action === 'delete' || action === 'deleteMany') return 'HIGH';
  if (highRiskModels.includes(model)) return 'HIGH';
  if (mediumRiskModels.includes(model)) return 'MEDIUM';
  
  return 'LOW';
}

export function enhancedAuditLogMiddleware(): Prisma.Middleware {
  return async (params, next) => {
    const isAuditedModel = AUDITED_MODELS.includes(params.model as Prisma.ModelName);
    const isWriteAction = WRITE_ACTIONS.includes(params.action);
    const isSensitiveRead = SENSITIVE_READ_ACTIONS.includes(params.action) && 
                           SENSITIVE_MODELS.includes(params.model as Prisma.ModelName);

    // Auditer les écritures sur tous les modèles auditables et les lectures sensibles
    if (!isAuditedModel || (!isWriteAction && !isSensitiveRead)) {
      return next(params);
    }

    const context = enhancedAuditContext.getStore();

    if (!context || context.userId === undefined) {
      if (params.model !== 'AuditLog') {
        console.warn(
          `[ENHANCED AUDIT] Contexte d'audit manquant pour ${params.action} sur ${params.model}. ` +
          `L'opération va continuer mais ne sera pas auditée.`
        );
      }
      return next(params);
    }

    const { model, action, args } = params;
    let entiteId: string | undefined;
    let anciennesValeurs: any = null;
    let nouvellesValeurs: any = null;

    // Récupérer les anciennes valeurs pour les updates et deletes
    if ((action === 'update' || action === 'delete') && args.where?.id && model) {
      try {
        const prismaGlobal = (await import('@/lib/prisma')).default;
        // Utilisation d'une approche plus sûre pour accéder aux modèles Prisma
        const modelDelegate = (prismaGlobal as any)[model.toLowerCase()];
        if (modelDelegate && typeof modelDelegate.findUnique === 'function') {
          const existingRecord = await modelDelegate.findUnique({
            where: args.where
          });
          anciennesValeurs = existingRecord;
        }
        entiteId = String(args.where.id);
      } catch (error) {
        console.warn(`[ENHANCED AUDIT] Impossible de récupérer les anciennes valeurs pour ${model}:`, error);
      }
    }

    // Capturer les nouvelles valeurs
    if (action === 'update' || action === 'updateMany' || action === 'upsert') {
      nouvellesValeurs = args.data;
      if (args.where?.id) entiteId = String(args.where.id);
    } else if (action === 'create' || action === 'createMany') {
      nouvellesValeurs = args.data;
    }

    // Exécuter l'opération
    const result = await next(params);

    // Récupérer l'ID pour les créations
    if (action === 'create' && result?.id) {
      entiteId = String(result.id);
    }

    // Créer l'entrée d'audit si on a un utilisateur
    if (context.userId !== undefined && model && params.model !== 'AuditLog') {
      const businessAction = getBusinessAction(model, action, nouvellesValeurs);
      const criticalityLevel = getCriticalityLevel(model, action);
      
      // Construire la description détaillée
      let description = `${businessAction} sur ${model}`;
      if (entiteId) description += ` (ID: ${entiteId})`;
      if (context.module) description += ` - Module: ${context.module}`;
      if (context.operation) description += ` - Opération: ${context.operation}`;

      try {
        const prismaGlobal = (await import('@/lib/prisma')).default;
        await prismaGlobal.auditLog.create({
          data: {
            utilisateurId: context.userId,
            action: businessAction,
            entite: model,
            entiteId: entiteId,
            ancienValeurs: anciennesValeurs ? JSON.parse(JSON.stringify(anciennesValeurs)) : Prisma.JsonNull,
            nouvellesValeurs: nouvellesValeurs ? JSON.parse(JSON.stringify(nouvellesValeurs)) : Prisma.JsonNull,
            adresseIp: context.ip,
            userAgent: context.userAgent,
            descriptionAction: description,
            sessionId: context.sessionId,
            requestId: context.requestId,
            module: context.module,
            operation: context.operation,
            resourceId: context.resourceId,
            criticalityLevel,
            metadata: context.metadata ? JSON.parse(JSON.stringify(context.metadata)) : Prisma.JsonNull
          },
        });
      } catch (auditError) {
        console.error("[ENHANCED AUDIT] Échec de l'écriture du log d'audit:", auditError);
        // En cas d'erreur d'audit, on peut choisir de continuer ou d'échouer
        // Pour la sécurité, on pourrait vouloir échouer les opérations critiques
        if (criticalityLevel === 'CRITICAL') {
          console.error("[ENHANCED AUDIT] Opération critique - échec de l'audit pourrait nécessiter un rollback");
        }
      }
    }

    return result;
  };
}

// Fonction utilitaire pour créer un contexte d'audit enrichi
export function createAuditContext(
  userId: number,
  ip?: string,
  userAgent?: string,
  options?: {
    sessionId?: string;
    requestId?: string;
    module?: string;
    operation?: string;
    resourceId?: string;
    metadata?: Record<string, any>;
  }
): AuditContext {
  return {
    userId,
    ip,
    userAgent,
    sessionId: options?.sessionId,
    requestId: options?.requestId,
    module: options?.module,
    operation: options?.operation,
    resourceId: options?.resourceId,
    metadata: options?.metadata
  };
}

// Fonction pour auditer des actions personnalisées (hors Prisma)
export async function logCustomAuditEvent(
  action: BusinessAction | string,
  details: {
    entite?: string;
    entiteId?: string;
    description?: string;
    metadata?: Record<string, any>;
    criticalityLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }
) {
  const context = enhancedAuditContext.getStore();
  
  if (!context || !context.userId) {
    console.warn('[ENHANCED AUDIT] Contexte manquant pour l\'audit personnalisé');
    return;
  }

  try {
    const prismaGlobal = (await import('@/lib/prisma')).default;
    await prismaGlobal.auditLog.create({
      data: {
        utilisateurId: context.userId,
        action: String(action),
        entite: details.entite,
        entiteId: details.entiteId,
        descriptionAction: details.description || String(action),
        adresseIp: context.ip,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        module: context.module,
        operation: context.operation,
        criticalityLevel: details.criticalityLevel || 'MEDIUM',
        metadata: JSON.parse(JSON.stringify({
          customEvent: true,
          timestamp: new Date().toISOString(),
          ...details.metadata,
          ...context.metadata
        }))
      }
    });
  } catch (error) {
    console.error('[ENHANCED AUDIT] Erreur lors de l\'audit personnalisé:', error);
  }
}